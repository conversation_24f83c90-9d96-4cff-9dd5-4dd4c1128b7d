<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item :name="1">
            <template slot="title">
              到货计划详情<span class="title-code">{{
                basicFormModel.planCode
              }}</span>
            </template>
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="purchaseOrderNo" label="关联采购单号：">
                    <el-input
                      v-model="basicFormModel.purchaseOrderNo"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="planStatus" label="计划状态：">
                    <el-select
                      v-model="basicFormModel.planStatus"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option value="1" label="待发货" />
                      <el-option value="2" label="已发货" />
                      <el-option value="3" label="已收货" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="materialName" label="物料名称：">
                    <el-input
                      v-model="basicFormModel.materialName"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialCode" label="物料编码：">
                    <el-input
                      v-model="basicFormModel.materialCode"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="planQuantity" label="计划数量：">
                    <el-input
                      style="width: 90%"
                      v-model="basicFormModel.planQuantity"
                      placeholder=""
                    />
                    <span style="position: absolute; margin-left: 10px">{{
                      basicFormModel.unitName || "kg"
                    }}</span>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item prop="planDeliveryTime" label="计划交货时间：">
                    <el-date-picker
                      v-model="basicFormModel.planDeliveryTime"
                      type="date"
                      value-format="yyyy-MM-dd"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="supplierName" label="供应商名称：">
                    <el-input
                      v-model="basicFormModel.supplierName"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="entPrefix" label="企业前缀：">
                    <el-input
                      v-model="basicFormModel.entPrefix"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receiveAddress" label="收货地址：">
                    <el-input
                      v-model="basicFormModel.receiveAddress"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="计划备注：">
                    <el-input
                      v-model="basicFormModel.remark"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            title="发货信息"
            :name="2"
            v-if="planStatus !== '1'"
          >
            <span class="table-title" style="text-align: center"
              >本计划发货明细</span
            >
            <el-table
              ref="table"
              border
              :data="detailTableData"
              show-summary
              :summary-method="customSummaryMethod"
              summary-cell-class-name="bold-summary"
            >
              <template v-for="(item, index) in detailColumns.data">
                <el-table-column
                  :prop="item.prop"
                  :label="item.label"
                  :key="index"
                >
                  <template slot-scope="scope">
                    <span v-if="item.prop === 'index'">{{
                      scope.$index + 1
                    }}</span>
                    <span
                      v-else-if="item.prop === 'actualQuantity'"
                      style="
                        text-align: right;
                        display: inline-block;
                        width: 100%;
                      "
                    >
                      {{ scope.row[item.prop] }}
                      {{ basicFormModel.unitName || "kg" }}
                    </span>
                    <span v-else>{{ scope.row[item.prop] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>

            <el-form
              label-position="top"
              :model="orderDetailFormModel"
              :disabled="true"
            >
              <el-row :gutter="20">
                <!--                <el-col :span="12">-->
                <!--                  <el-form-item prop="deliveryOrderNo" label="发货单号：">-->
                <!--                    <el-input-->
                <!--                      v-model="orderDetailFormModel.deliveryOrderNo"-->
                <!--                      placeholder=""-->
                <!--                    />-->
                <!--                  </el-form-item>-->
                <!--                </el-col>-->
                <el-col :span="12">
                  <el-form-item prop="deliveryTime" label="发货时间：">
                    <el-date-picker
                      v-model="orderDetailFormModel.deliveryTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="transportType" label="运输类型：">
                    <el-input
                      v-model="orderDetailFormModel.transportType"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="driverPhone" label="司机电话：">
                    <el-input
                      v-model="orderDetailFormModel.driverPhone"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="licensePlate" label="车牌号：">
                    <el-input
                      v-model="orderDetailFormModel.licensePlate"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row
                :gutter="20"
                v-if="orderDetailFormModel.deliveryRemarkEnabled"
              >
                <el-col :span="24">
                  <el-form-item prop="deliveryRemark" label="发货备注：">
                    <el-input
                      type="textarea"
                      v-model="orderDetailFormModel.deliveryRemark"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="delivery_image" label="图片：">
                    <div class="image-list">
                      <el-image
                        v-for="(url, index) in deliveryImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="deliveryImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>

          <el-collapse-item
            title="收货信息"
            :name="3"
            v-if="planStatus === '3'"
          >
            <el-form
              v-if="flag === 'detail'"
              ref="deliveryWork"
              label-position="top"
              :model="deliveryWorkForm"
              :disabled="true"
            >
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiptRemark" label="备注：">
                    <!-- <el-switch
                      v-model="takeDeliveryFormModel.receiptRemarkEnabled"
                      active-text="备注给对方"
                      inactive-text=""
                    /> -->
                    <el-input
                      v-model="takeDeliveryFormModel.receiptRemark"
                      type="textarea"
                      :rows="3"
                      placeholder=""
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="receiveImages" label="收货凭证：">
                    <div class="image-list">
                      <el-image
                        v-for="(
                          url, index
                        ) in takeDeliveryFormModel.receiptImages"
                        :key="index"
                        :src="url"
                        :preview-src-list="takeDeliveryFormModel.receiptImages"
                        style="width: 150px; height: 150px; margin-right: 10px"
                        fit="cover"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <el-form
              label-position="top"
              :model="operationFormModel"
              :rules="operationFormRules"
              v-else
              ref="operationForm"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="receipt_quantity" label="实收数量：">
                    <el-input
                      v-model="operationFormModel.receipt_quantity"
                      placeholder="请输入实收数量"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <!--        <el-button type="primary" @click="save">确认收货</el-button>-->
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import VirtualTable from "@/components/VirtualTable";
import DataTable from "@/components/DataTable/index.vue";
import acc from "@/api/acc/acc";

export default {
  name: "ArrivalPlanAdd",
  components: { DataTable, VirtualTable },
  data() {
    return {
      collapse: [1, 2, 3],
      flag: "",
      basicFormModel: {
        planCode: "", // 计划编号
        purchaseOrderNo: "", // 关联采购单号
        supplierName: "", // 供应商名称
        unitName: "", // 单位
        entPrefix: "", // 供应商企业前缀
        materialCode: "", // 物料编码
        materialName: "", // 物料名称
        planQuantity: "", // 计划数量
        planDeliveryTime: "", // 计划交货时间
        remark: "", // 计划备注
        remarkSwitch: 0, // 计划备注开关0关闭 1开启
        receiveAddress: "", // 收货地址
        statisticType: "1", // 保留原有字段
      },
      planStatus: "",
      basicFormRules: {},
      detailColumns: {
        data: [
          {
            prop: "index",
            label: "序号",
            sortable: false,
          },
          {
            label: "工业互联网标识码",
            prop: "identificationCode",
            sortable: false,
          },
          {
            label: "药材产品名称（供）",
            prop: "herbProductName",
            sortable: false,
          },
          {
            label: "生产批号",
            prop: "productionBatchNo",
            sortable: false,
          },
          {
            label: "实发数量（单位）",
            prop: "actualQuantity",
            slotName: "actualQuantity",
            sortable: false,
          },
        ],
      },
      detailTableData: [
        { id: "1", name: "黄芪", code: "HP20231001", count: "100" },
        { id: "2", name: "党参", code: "DC20231002", count: "200" },
        { id: "3", name: "当归", code: "DG20231003", count: "150" },
      ],
      orderDetailFormModel: {},
      deliveryImages: [],
      receiptImages: [],
      takeDeliveryFormModel: {
        receiptRemark: "",
        receiptImages: "",
        receiptRemarkEnabled: false,
      },
      operationFormModel: {
        receipt_quantity: "",
      },
      operationFormRules: {
        receipt_quantity: [{ required: true, message: "请输入实收数量" }],
      },
    };
  },
  mounted() {
    // 确保汇总行样式生效
    this.$nextTick(() => {
      setTimeout(() => {
        // 直接查找并设置汇总行样式
        const summaryRows = document.querySelectorAll(
          ".el-table__footer tr td"
        );
        summaryRows.forEach((cell) => {
          cell.style.fontWeight = "bold";
          cell.style.textAlign = "right";
        });
      }, 100);
      acc
        .getDeliveryPlanDetail({ id: this.$route.query.id })
        .then((res) => {
          if (res.data) {
            const data = res.data;
            this.planStatus = data.planStatus;
            // 绑定基本信息
            this.basicFormModel = {
              planCode: data.planCode || "",
              planStatus: data.planStatus || "",
              purchaseOrderNo: data.purchaseOrderNo || "",
              supplierName: data.supplierName || "",
              unitName: data.unitName || "",
              entPrefix: data.entPrefix || "",
              materialCode: data.materialCode || "",
              materialName: data.materialName || "",
              planQuantity: data.planQuantity,
              planDeliveryTime: data.planDeliveryTime || "",
              remark: data.remark || "",
              remarkSwitch: data.remarkSwitch || 0,
              receiveAddress: data.receiveAddress || "",
              statisticType: "1", // 保留原有字段
            };

            // 绑定发货信息
            this.orderDetailFormModel = {
              deliveryOrderNo: "",
              totalActualQuantity: data.totalActualQuantity || "",
              deliveryTime: data.deliveryTime || "",
              transportType: this.getTransportTypeText(data.transportType),
              driverPhone: data.driverPhone || "",
              licensePlate: data.licensePlate || "",
              deliveryRemark: data.deliveryRemark || "",
              deliveryRemarkEnabled:
                data.deliveryRemarkEnabled === 1 ? true : false,
            };

            // 绑定收货信息
            this.takeDeliveryFormModel = {
              receiptRemark: data.receiptRemark,
              receiptImages: data.receiptImages || [],
              receiptRemarkEnabled:
                data.receiptRemarkEnabled == 1 ? true : false,
            };

            // 绑定操作表单
            this.operationFormModel = {
              receiptQuantity: data.receiptQuantity,
            };

            // 绑定药材列表到表格
            this.herbList = data.herbList || [];
            this.detailTableData = this.herbList;

            // 绑定图片
            this.deliveryImages = data.deliveryImages || [];

            // 更新标题中的计划编号
            this.$nextTick(() => {
              const titleElement = document.querySelector(".title-code");
              if (titleElement && data.planCode) {
                titleElement.textContent = data.planCode;
              }
            });
          }
        })
        .catch((error) => {
          console.error("获取到货计划详情失败:", error);
          this.$message.error("获取详情失败，请重试");
        });
    });
    this.flag = this.$route.query.action;
  },
  methods: {
    back() {
      this.$router.replace({
        path: "arrivalPlan",
        query: { activeName: this.$route.query.activeName },
      });
    },

    customSummaryMethod(params) {
      const { columns, data } = params;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 3) {
          // 第四列显示汇总标题
          sums[index] = "发货总数量:";
        } else if (column.property === "actualQuantity") {
          const values = data.map((item) => Number(item[column.property]));
          const total = values.reduce((prev, curr) => {
            const value = Number(curr);
            return isNaN(value) ? prev : prev + curr;
          }, 0);
          sums[index] = `${total} ${this.basicFormModel.unitName || "kg"}`;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },

    // 获取运输类型文本
    getTransportTypeText(type) {
      const typeMap = {
        1: "自有车辆",
        2: "三方车辆",
      };
      return typeMap[type] || type || "";
    },

    save() {
      this.$refs["operationForm"].validate((valid) => {
        console.log(valid);
        if (!valid) {
          this.$message.error("请输入实收数量");
          return;
        } else {
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
.title-code {
  color: #8a8989;
  font-size: 12px;
  margin-left: 20px;
}
.table-title {
  text-align: center;
  width: 100%;
  display: inline-block;
  line-height: 30px;
  margin-top: 10px;
  font-size: 18px;
  font-weight: 700;
}
/* 直接针对汇总行的样式 */
.el-table__footer tr td {
  font-weight: bold !important;
}
::v-deep .el-table thead {
  color: #000 !important;
}

::v-deep .el-table thead tr th {
  color: #000 !important;
}
</style>
