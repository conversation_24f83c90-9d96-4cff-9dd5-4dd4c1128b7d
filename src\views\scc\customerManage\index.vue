<template>
  <div class="app-container">
    <!--    &lt;!&ndash; 搜索框 &ndash;&gt;-->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="
        (data) => {
          searchHelper.search(data);
        }
      "
      @return-reset="searchHelper.reset"
    >
    </data-select>
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :pagination.sync="searchHelper.pagination"
      @search-event="
        () => {
          searchHelper.handleQuery();
        }
      "
    >
      <template v-slot:statusName="{ row }">
        <el-tag v-if="row.statusName == '协同中'" type="success">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '待对方接受'" type="primary">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '待我方确认'" type="warning">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '已冻结'" type="danger">{{
          row.statusName
        }}</el-tag>
        <el-tag v-if="row.statusName == '未达成协同'" type="info">{{
          row.statusName
        }}</el-tag>
      </template>
    </data-table>
    <simple-data-dialog
      class="dialog-style"
      :title="
        dialogMode === 'confirm'
          ? '客户信息确认'
          : dialogMode === 'edit'
          ? '编辑客户信息'
          : '邀请客户'
      "
      :visible="aoTuMationDialogVisible"
      size="middle"
      :show-close="true"
      :before-close="handleBeforeClose"
      :check-dirty="true"
    >
      <el-form label-width="80px" v-if="dialogMode === 'add'">
        <el-form-item
          prop="identificationRuleId"
          label="企业选择："
          style="width: 90%"
        >
          <el-select
            v-model="companyName"
            filterable
            remote
            reserve-keyword
            clearable
            placeholder="企业前缀/企业名称/统一社会信用代码"
            :remote-method="remoteSearchCompany"
            :loading="searchLoading"
            @change="changeCompany($event)"
            @visible-change="onSelectVisibleChange"
          >
            <div
              v-infinite-scroll="loadMoreCompanies"
              class="infinite-list"
              :infinite-scroll-disabled="noMoreCompanies || loadingMore"
              :infinite-scroll-distance="10"
            >
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="item.companyName"
                :value="item.entCode"
              >
                <div>
                  <div
                    style="
                      width: 18vw;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    "
                    :title="item.companyName"
                  >
                    {{ item.companyName }}
                  </div>
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      color: #8492a6;
                      font-size: 13px;
                    "
                  >
                    <span>{{ item.entPrefix }}</span>
                    <span>{{ item.entCode }}</span>
                  </div>
                </div>
              </el-option>
              <div v-if="loadingMore" class="loading-more">
                <i class="el-icon-loading"></i>
                <span>加载中...</span>
              </div>
            </div>
          </el-select>
        </el-form-item>
      </el-form>
      <el-divider v-if="dialogMode === 'add'"></el-divider>
      <div class="info-form">
        <el-form label-width="120px">
          <el-form-item
            v-for="item in formItems"
            :key="item.prop"
            :label="item.label"
            class="right-label"
          >
            <template v-if="item.prop === 'businessLicense'">
              <magnify-img
                v-if="form[item.prop]"
                :src="form[item.prop]"
                :width="300"
                :height="300"
                :zoom="2"
                :pop-width="400"
                :pop-height="400"
                :watch-size="120"
              />
            </template>
            <template v-else-if="item.prop === 'legalPersonName'">
              <div v-if="!!form[item.prop]" style="width: 300px">
                <span style="margin-right: 10px">{{ form[item.prop] }}</span>
                <el-tag size="mini">已实名核验</el-tag>
              </div>
            </template>
            <template
              v-else-if="
                item.prop === 'contactPersonName' ||
                item.prop === 'contactPersonPhone' ||
                item.prop === 'customerCode'
              "
            >
              <span
                v-if="
                  !!companyName ||
                  dialogMode === 'edit' ||
                  dialogMode === 'confirm'
                "
              >
                <el-input v-model="form[item.prop]" style="width: 300px" />
              </span>
            </template>
            <span v-else class="info-value">{{ form[item.prop] || "" }}</span>
          </el-form-item>
        </el-form>
      </div>

      <el-footer class="button-container">
        <!--        <el-button @click="closeDialog">取消</el-button>-->
        <el-button
          v-if="dialogMode === 'add'"
          type="primary"
          @click="submitAoToMation"
          >发出邀请</el-button
        >
        <el-button
          v-if="dialogMode === 'confirm'"
          type="danger"
          @click="handleReject"
          >拒绝</el-button
        >
        <el-button
          v-if="dialogMode === 'confirm'"
          type="success"
          @click="handleConfirm"
          >确认</el-button
        >
        <el-button
          v-if="dialogMode === 'edit'"
          type="primary"
          @click="handleSubmitEdit"
          >保存</el-button
        >
      </el-footer>
    </simple-data-dialog>

    <simple-data-dialog
      title="详情"
      :visible="showDetail"
      size="middle"
      :show-close="true"
      class="dialog-style"
      :check-dirty="true"
      :before-close="handleDetailClose"
    >
      <div class="info-form">
        <el-form label-width="120px">
          <el-form-item
            v-for="item in formItems"
            :key="item.prop"
            :label="item.label"
            class="right-label"
          >
            <template v-if="item.prop === 'businessLicense'">
              <magnify-img
                v-if="form[item.prop]"
                :src="form[item.prop]"
                :width="300"
                :height="300"
                :zoom="2"
                :pop-width="400"
                :pop-height="400"
                :watch-size="120"
              />
            </template>
            <template v-else-if="item.prop === 'legalPersonName'">
              <div v-if="!!form[item.prop]" style="width: 300px">
                <span style="margin-right: 10px">{{ form[item.prop] }}</span>
                <el-tag size="mini">已实名核验</el-tag>
              </div>
            </template>
            <span v-else class="info-value">{{ form[item.prop] || "" }}</span>
          </el-form-item>
        </el-form>
      </div>

      <el-footer class="button-container"> </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import MagnifyImg from "@/components/MagnifyImg/index.vue"; // 引入 magnifyImg 组件
import accApi from "@/api/acc/acc";

export default {
  name: "MaterialList",
  components: { SimpleDataDialog, DataTable, DataSelect, MagnifyImg }, // 注册 magnifyImg 组件
  data() {
    return {
      statusMap: ["协同中", "待对方接收", "待我方审核", "已冻结", "未达成协同"],
      // 搜索组件数据
      search: {
        searchKey: {
          label: "客户编码/客户名称/企业前缀",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入客户编码/客户名称/企业前缀",
          },
        },
        status: {
          label: "状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: "协同中", label: "协同中" },
              { value: "待对方接受", label: "待对方接受" },
              { value: "待我方确认", label: "待我方确认" },
              { value: "已冻结", label: "已冻结" },
              { value: "未达成协同", label: "未达成协同" },
            ],
            placeholder: "请选择同步状态",
          },
        },
      },
      buttonData: [
        {
          label: "添加",
          action: this.onAddClick,
          permission: "all",
        },
      ],
      searchHelper: new this.$searchHelper({ api: accApi.queryCustomerList }),
      // 表格数据data
      tempData: {
        pageNum: 1,
        pageSize: 10,
        pages: 2,
        prePage: 0,
        list: [],
      },
      // 表格列,按钮数据
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            sortable: false,
          },
          {
            label: "企业前缀",
            prop: "customerEntPrefix",
            sortable: false,
          },
          {
            label: "协同状态",
            prop: "statusName",
            sortable: false,
            slotName: "statusName",
          },
          {
            label: "客户名称",
            prop: "companyName",
            sortable: false,
          },
          {
            label: "统一社会信用代码",
            prop: "customerEntCode",
            sortable: false,
          },
          {
            label: "客户编码",
            prop: "customerCode",
            sortable: false,
          },
          {
            label: "地址",
            prop: "companyAddress",
            sortable: false,
          },
          {
            label: "联系人",
            prop: "customerContactPerson",
            sortable: false,
          },
          {
            label: "联系电话",
            prop: "customerContactPhone",
            sortable: false,
          },
        ],
        operation: {
          label: "操作",
          width: "220px",
          data: (row) => {
            let button = [];
            switch (row.statusName) {
              case "协同中":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "编辑",
                    action: this.onEditClick,
                    permission: "all",
                  },
                ];
                break;
              case "待对方接受":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                  {
                    label: "撤销",
                    action: this.onCancel,
                    permission: "all",
                  },
                ];
                break;
              case "待我方确认":
                button = [
                  {
                    label: "确认",
                    action: this.onConfirm,
                    permission: "all",
                  },
                ];
                break;
              case "已冻结":
                button = [
                  {
                    label: "详情",
                    action: this.onDetail,
                    permission: "all",
                  },
                ];
                break;
              case "未达成协同":
                button = [
                  {
                    label: "删除",
                    action: this.onDeleteClick,
                    permission: "all",
                  },
                ];
                break;
            }
            return button;
          },
        },
      },
      aoTuMationDialogVisible: false,
      dialogMode: "add",
      showDetail: false,
      templateList: [],
      ruleList: [],
      options: [],
      companyName: "",
      searchLoading: false,
      // 分页加载相关
      companyPagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      loadingMore: false,
      noMoreCompanies: false,
      currentSearchQuery: "",
      form: {
        entPrefix: "",
        companyName: "",
        entCode: "",
        detailedAddress: "",
        businessLicense: "",
        legalPersonName: "",
        contactPersonName: "",
        contactPersonPhone: "",
        customerCode: "",
      },
      formItems: [
        { prop: "entPrefix", label: "企业前缀:" },
        { prop: "companyName", label: "客户名称:" },
        { prop: "entCode", label: "统一社会信用代码:" },
        { prop: "detailedAddress", label: "地址:" },
        { prop: "businessLicense", label: "营业执照:" },
        { prop: "legalPersonName", label: "法定代表人:" },
        { prop: "contactPersonName", label: "联系人:" },
        { prop: "contactPersonPhone", label: "联系电话:" },
        { prop: "customerCode", label: "客户编码:" },
      ],
      labelPosition: "right",
      companyData: [],
      showMagnifier: false,
      magnifierStyle: {},
      magnifierImageStyle: {},
      showSelection: false,
      selectionStyle: {},
      originalFormInDialog: null,
    };
  },
  mounted() {
    this.searchHelper.handleQuery();
  },
  methods: {
    // 远程搜索企业
    async remoteSearchCompany(query) {
      this.currentSearchQuery = query || "";
      this.resetCompanyPagination();

      this.searchLoading = true;
      try {
        const res = await accApi.queryCompanyInfo({
          companyName: query,
          pageNum: this.companyPagination.pageNum,
          pageSize: this.companyPagination.pageSize,
        });
        this.options = res.data.list || [];
        this.companyPagination.total = res.data.total || 0;
        this.noMoreCompanies =
          this.options.length < this.companyPagination.pageSize;
      } catch (error) {
        console.error("搜索企业失败:", error);
        this.options = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },

    // 重置分页信息
    resetCompanyPagination() {
      this.companyPagination.pageNum = 1;
      this.companyPagination.total = 0;
      this.noMoreCompanies = false;
      this.loadingMore = false;
    },

    // 加载更多企业数据
    async loadMoreCompanies() {
      if (this.noMoreCompanies || this.loadingMore) {
        return;
      }

      this.loadingMore = true;
      this.companyPagination.pageNum++;

      try {
        const res = await accApi.queryCompanyInfo({
          companyName: this.currentSearchQuery,
          pageNum: this.companyPagination.pageNum,
          pageSize: this.companyPagination.pageSize,
        });

        const newOptions = res.data.list || [];
        this.options = [...this.options, ...newOptions];

        // 判断是否还有更多数据
        if (newOptions.length < this.companyPagination.pageSize) {
          this.noMoreCompanies = true;
        }
      } catch (error) {
        console.error("加载更多企业失败:", error);
        this.companyPagination.pageNum--; // 回退页码
      } finally {
        this.loadingMore = false;
      }
    },

    // 下拉框显示/隐藏事件
    onSelectVisibleChange(visible) {
      if (!visible) {
        // 下拉框关闭时重置状态
        this.resetCompanyPagination();
      }
    },
    // 确认
    onConfirm(row) {
      accApi.queryCustomerDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.customerEntPrefix;
        this.form.companyName = res.data.customerEntName;
        this.form.entCode = res.data.customerEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.customerContactPerson;
        this.form.contactPersonPhone = res.data.customerContactPhone;
        this.form.customerCode = res.data.customerCode;
        this.form.id = row.id;
        this.dialogMode = "confirm";
        this.aoTuMationDialogVisible = true;
        this.originalFormInDialog = JSON.parse(JSON.stringify(this.form));
      });
    },
    // 撤销
    onCancel(row) {
      this.$confirm("是否确定撤销？", "提示", { type: "warning" }).then(() => {
        accApi.delCustomer({ id: row.id }).then((res) => {
          this.$message({
            message: "撤销成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    // 详情
    onDetail(row) {
      accApi.queryCustomerDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.customerEntPrefix;
        this.form.companyName = res.data.customerEntName;
        this.form.entCode = res.data.customerEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.customerContactPerson;
        this.form.contactPersonPhone = res.data.customerContactPhone;
        this.form.customerCode = res.data.customerCode;
        this.showDetail = true;
      });
    },
    // 发出邀请
    submitAoToMation() {
      if (this.companyName) {
        accApi
          .addCustomer({
            customerCode: this.form.customerCode,
            customerEntCode: this.form.entCode,
            customerEntPrefix: this.form.entPrefix,
            customerContactPerson: this.form.contactPersonName,
            customerContactPhone: this.form.contactPersonPhone,
          })
          .then((res) => {
            this.$message({
              message: "邀请成功",
              type: "success",
            });
            this.aoTuMationDialogVisible = false;
            this.searchHelper.handleQuery();
          });
      } else {
        this.$message.error("请先选择企业");
      }
    },
    // 确认
    handleConfirm() {
      accApi
        .editCustomer({
          id: this.form.id,
          customerCode: this.form.customerCode,
          customerEntCode: this.form.entCode,
          customerEntPrefix: this.form.entPrefix,
          customerContactPerson: this.form.contactPersonName,
          customerContactPhone: this.form.contactPersonPhone,
          status: 5,
        })
        .then((res) => {
          this.$message({
            message: "确认成功",
            type: "success",
          });
          this.aoTuMationDialogVisible = false;
          this.searchHelper.handleQuery();
        });
    },
    // 删除
    onDeleteClick(row) {
      this.$confirm("是否删除？", "提示", { type: "warning" }).then(() => {
        accApi.delCustomer({ id: row.id }).then((res) => {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.searchHelper.handleQuery();
        });
      });
    },
    handleReject() {
      this.$confirm("是否确定拒绝？", "提示", { type: "warning" }).then(() => {
        accApi
          .updateSupplier({
            id: this.form.id,
            status: 4,
          })
          .then((res) => {
            this.$message({
              message: "拒绝成功",
              type: "success",
            });
            this.aoTuMationDialogVisible = false;
            this.searchHelper.handleQuery();
          });
      });
    },
    handleSubmitEdit() {
      accApi
        .editCustomer({
          id: this.form.id,
          customerCode: this.form.customerCode,
          customerEntCode: this.form.entCode,
          customerEntPrefix: this.form.entPrefix,
          customerContactPerson: this.form.contactPersonName,
          customerContactPhone: this.form.contactPersonPhone,
        })
        .then((res) => {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this.aoTuMationDialogVisible = false;
          this.searchHelper.handleQuery();
        });
    },
    onEditClick(row) {
      accApi.queryCustomerDetail({ id: row.id }).then((res) => {
        this.form.entPrefix = res.data.customerEntPrefix;
        this.form.companyName = res.data.customerEntName;
        this.form.entCode = res.data.customerEntCode;
        this.form.detailedAddress = res.data.address;
        this.form.businessLicense = res.data.businessLicense;
        this.form.legalPersonName = res.data.legalPersonName;
        this.form.contactPersonName = res.data.customerContactPerson;
        this.form.contactPersonPhone = res.data.customerContactPhone;
        this.form.customerCode = res.data.customerCode;
        this.form.id = row.id;
        this.dialogMode = "edit";
        this.aoTuMationDialogVisible = true;
        this.originalFormInDialog = JSON.parse(JSON.stringify(this.form));
      });
    },
    onAddClick() {
      this.currentSearchQuery = "";
      this.resetCompanyPagination();
      try {
        this.searchLoading = true;
        accApi
          .queryCompanyInfo({
            companyName: "",
            pageNum: this.companyPagination.pageNum,
            pageSize: this.companyPagination.pageSize,
          })
          .then((res) => {
            this.options = res.data.list || [];
            this.companyPagination.total = res.data.total || 0;
            this.noMoreCompanies =
              this.options.length < this.companyPagination.pageSize;
            this.dialogMode = "add";
            this.form = {
              entPrefix: "",
              companyName: "",
              entCode: "",
              detailedAddress: "",
              businessLicense: "",
              legalPersonName: "",
              contactPersonName: "",
              contactPersonPhone: "",
              customerCode: "",
            };
            this.companyName = undefined;
            this.aoTuMationDialogVisible = true;
          });
      } catch (error) {
        console.error("搜索企业失败:", error);
        this.options = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },
    changeCompany(entCode) {
      console.log(entCode, "search company");
      if (entCode) {
        // 选择了企业
        const selectedCompany = this.options.find(
          (company) => company.entCode === entCode
        );
        if (selectedCompany) {
          this.form = {
            entPrefix: selectedCompany.entPrefix,
            companyName: selectedCompany.companyName,
            entCode: selectedCompany.entCode,
            detailedAddress: selectedCompany.detailedAddress,
            businessLicense: selectedCompany.businessLicense,
            legalPersonName: selectedCompany.legalPersonName,
            contactPersonName: selectedCompany.contactPersonName,
            contactPersonPhone: selectedCompany.contactPersonPhone,
            customerCode: selectedCompany.customerCode,
          };
        }
      } else {
        // 清空了企业选择，重置表单并重新获取企业列表
        this.form = {
          entPrefix: "",
          companyName: "",
          entCode: "",
          detailedAddress: "",
          businessLicense: "",
          legalPersonName: "",
          contactPersonName: "",
          contactPersonPhone: "",
          customerCode: "",
        };
        this.currentSearchQuery = "";
        this.resetCompanyPagination();
        try {
          this.searchLoading = true;
          // 重新获取企业列表
          accApi
            .queryCompanyInfo({
              companyName: "",
              pageNum: this.companyPagination.pageNum,
              pageSize: this.companyPagination.pageSize,
            })
            .then((res) => {
              this.options = res.data.list || [];
              this.companyPagination.total = res.data.total || 0;
              this.noMoreCompanies =
                this.options.length < this.companyPagination.pageSize;
            })
            .catch((error) => {
              console.error("获取企业列表失败:", error);
              this.options = [];
            });
        } catch (error) {
          console.error("搜索企业失败:", error);
          this.options = [];
          this.noMoreCompanies = true;
        } finally {
          this.searchLoading = false;
        }
      }
    },
    handleDetailClose() {
      this.showDetail = false;
    },
    handleBeforeClose() {
      console.log("beforeClose", this.form, this.originalFormInDialog);
      const hasChanged = !this.$util.isEqual(
        this.form,
        this.originalFormInDialog
      );
      if (hasChanged) {
        return new Promise((resolve, reject) => {
          this.$confirm(
            "当前页面信息有修改，关闭页面将不保存修改，是否确认关闭？",
            "提示",
            {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              this.aoTuMationDialogVisible = false;
              resolve(); // 允许关闭
            })
            .catch(() => {
              reject(); // 阻止关闭
            });
        });
      } else {
        this.aoTuMationDialogVisible = false;
        return true; // 不需要确认，直接关闭
      }
    },
    // closeDialog() {
    //   console.log('beforeClose', this.form, this.originalFormInDialog)
    //   const hasChanged = !this.$util.isEqual(this.form, this.originalFormInDialog);
    //   if (hasChanged) {
    //     this.$confirm('当前页面信息有修改，关闭页面将不保存修改，是否确认关闭？', '提示', {
    //       confirmButtonText: '确认',
    //       cancelButtonText: '取消',
    //       type: 'warning'
    //     }).then(() => {
    //       this.aoTuMationDialogVisible = false;
    //     }).catch(() => {
    //       // 用户点击取消，不关闭
    //     });
    //   } else {
    //     this.aoTuMationDialogVisible = false;
    //   }
    // },
  },
};
</script>
<style scoped lang="scss">
.el-select-dropdown__item {
  height: 66px;
}
.info-form {
  padding: 20px;
}

.info-value {
  display: inline-block;
  min-width: 450px;
  vertical-align: middle;
}

.info-img {
  width: 220px;
  height: 160px;
}

.el-form-item {
  margin-bottom: 12px;
}

::v-deep .info-form .el-form-item__label {
  padding-right: 10px;
  font-weight: 500;
  text-align: right !important;
  line-height: 32px;
}

.button-container {
  line-height: 60px;
  text-align: center;
}

::v-deep
  .dialog-style
  .el-dialog__header
  .el-dialog__headerbtn
  .el-dialog__close {
  display: inline-block !important;
}

.infinite-list {
  max-height: 300px;
  overflow-y: auto;
}

.loading-more {
  text-align: center;
  padding: 10px;
  color: #999;
  font-size: 14px;
}

.loading-more i {
  margin-right: 5px;
}
</style>
