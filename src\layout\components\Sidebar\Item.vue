<script>
export default {
  name: "MenuItem",
  functional: true,
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
  },
  render(h, context) {
    const { icon, title } = context.props;
    const vnodes = [];

    if (icon) {
      if (icon.includes("el-icon")) {
        vnodes.push(<i class={[icon, "sub-el-icon"]} />);
      } else if (icon.includes("fa-")) {
        vnodes.push(<i class={[icon, "fa"]} />);
      } else {
        vnodes.push(<svg-icon icon-class={icon} />);
      }
      // let iconPath = require("../../../assets/icon/" + icon + ".png");
      // vnodes.push(<img class="img_icon" src={iconPath} />);
    }

    if (title) {
      vnodes.push(<span slot="title">{title}</span>);
    }
    return vnodes;
  },
};
</script>

<style scoped>
.menu-level-1 i {
  margin: auto 15px !important;
  color: #ffffff;
  font-size: 18px;
}

.fa {
  margin-right: 12px;
  font-size: 18px;
}
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
.img_icon {
  width: 12px;
  height: 12px;
  margin-right: 8px;
}
.svg-icon {
  font-size: 18px !important;
  margin-left: 15px !important;
  margin-right: 15px !important;
}
</style>
