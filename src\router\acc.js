const acc = [
  {
    path: 'menuConf',
    name: 'MenuConf',
    component: () => import('@/views/acc/sysMg/menuConf')
  },
  {
    path: 'userView',
    name: 'UserView',
    preViewName: 'UserInfo',
    hidden: true,
    component: () => import('@/views/acc/sysMg/userInfo/UserView')
  },
  // 用户信息配置
  {
    path: 'userInfo',
    name: 'UserInfo',
    component: () => import('@/views/acc/sysMg/userInfo/UserInfo')
  },
  {
    path: 'bypassAccountView',
    name: 'BypassAccountView',
    preViewName: 'BypassAccount',
    hidden: true,
    component: () => import('@/views/companyRegist/bypassAccount/UserView')
  },
  // 用户信息配置
  {
    path: 'bypassAccount',
    name: 'BypassAccount',
    component: () => import('@/views/companyRegist/bypassAccount/UserInfo')
  },
  {
    path: 'internalRole',
    name: 'InternalRole',
    component: () => import('@/views/companyRegist/internalRole.vue')
  },
  // 角色权限配置
  {
    path: 'userRole',
    name: 'UserRole',
    component: () => import('@/views/acc/sysMg/userRole')
  },
  {
    path: 'product',
    name: 'Product',
    component: () => import('@/views/enterprise/productMg/product')
  },
  {
    path: 'productDetail',
    name: 'ProductDetail',
    hidden: true,
    preViewName: 'Product',
    component: () => import('@/views/enterprise/productMg/product/detail.vue')
  },
  {
    path: 'farmerInfo',
    name: 'FarmerInfo',
    component: () => import('@/views/watermalon/farmerInfo')
  },
  {
    path: 'farmerDetail',
    name: 'FarmerDetail',
    hidden: true,
    preViewName: 'FarmerInfo',
    component: () => import('@/views/watermalon/farmerInfo/detail.vue')
  },
  {
    path: 'productBatch',
    name: 'ProductBatch',
    component: () => import('@/views/enterprise/productMg/productBatch')
  }, {
    path: 'tagDetail',
    name: 'TagDetail',
    component: () => import('@/views/enterprise/tagMg/tagDetail')
  }, {
    path: 'tagOfDetail',
    name: 'TagOfDetail',
    preViewName: 'TagDetail',
    hidden: true,
    component: () => import('@/views/enterprise/tagMg/tagDetail/tagOfDetail')
  }, {
    path: 'tagGenerate',
    name: 'TagGenerate',
    component: () => import('@/views/enterprise/tagMg/tagGenerate')
  }, {
    path: 'tagRule',
    name: 'TagRule',
    component: () => import('@/views/enterprise/tagMg/tagRule')
  }, {
    path: 'tagRuleDetail',
    name: 'TagRuleDetail',
    preViewName: 'TagRule',
    hidden: true,
    component: () => import('@/views/enterprise/tagMg/tagRule/tagRuleDetail')
  },
  {
    path: 'productTemplate',
    name: 'ProductTemplate',
    component: () => import('@/views/enterprise/productMg/productTemplate')
  },
  {
    path: 'productTemplateDetail',
    name: 'ProductTemplateDetail',
    preViewName: 'ProductTemplate',
    hidden: true,
    component: () => import('@/views/enterprise/productMg/productTemplate/detail.vue')
  },
  {
    path: 'statistic',
    name: 'Statistic',
    component: () => import('@/views/enterprise/statisticAnalysis/statistic')
  }, {
    path: 'record',
    name: 'Record',
    component: () => import('@/views/enterprise/statisticAnalysis/record')
  },
  {
    path: 'dictMaintenance',
    name: 'DictMaintenance',
    component: () => import('@/views/dictionary/index')
  }, {
    path: 'dictMaintenanceDetail',
    name: 'DictMaintenanceDetail',
    preViewName: 'DictMaintenance',
    hidden: true,
    component: () => import('@/views/dictionary/detail')
  }, {
    path: 'log',
    name: 'Log',
    component: () => import('@/views/log/index')
  },
  {
    path: 'department',
    name: 'Department',
    component: () => import('@/views/department/index')
  }, {
    path: 'companyInfo',
    name: 'CompanyInfo',
    component: () => import('@/views/companyRegist/companyInfo')
  }, {
    path: 'companyAccount',
    name: 'CompanyAccount',
    component: () => import('@/views/companyRegist/companyAccount')
  },
  {
    path: 'companyAccountView',
    name: 'CompanyAccountView',
    preViewName: 'CompanyAccount',
    hidden: true,
    component: () => import('@/views/companyRegist/companyInfo')
  },
  {
    path: 'drugList',
    name: 'DrugList',
    component: () => import('@/views/drugManage/drugList/index.vue')
  },
  {
    path: 'drugListDetail',
    name: 'DrugListDetail',
    hidden: true,
    preViewName: 'DrugList',
    component: () => import('@/views/drugManage/drugList/detail.vue')
  },
  // 物料列表
  {
    path: 'materialList',
    name: 'materialList',
    component: () => import('@/views/materialManage/materialList/index.vue')
  },
  {
    path: 'materialDetail',
    name: 'MaterialDetail',
    hidden: true,
    preViewName: 'materialList',
    component: () => import('@/views/materialManage/materialList/detail.vue')
  },
  // 客户管理
  {
    path: 'customerManage',
    name: 'customerManage',
    component: () => import('@/views/scc/customerManage/index.vue')
  },
  // 供应商管理
  {
    path: 'supplierManage',
    name: 'supplierManage',
    component: () => import('@/views/scc/supplierManage/index.vue')
  },
  // 到货计划协同
  {
    path: 'arrivalPlan',
    name: 'arrivalPlan',
    component: () => import('@/views/scc/arrivalPlan/index.vue')
  },
  {
    path: 'arrivalPlanAdd',
    name: 'arrivalPlanAdd',
    hidden: true,
    preViewName: 'arrivalPlan',
    component: () => import('@/views/scc/arrivalPlan/add.vue')
  },
  {
    path: 'arrivalPlanDetail',
    name: 'arrivalPlanDetail',
    hidden: true,
    preViewName: 'arrivalPlan',
    component: () => import('@/views/scc/arrivalPlan/detail.vue')
  },
  {
    path: 'deliveryDetail',
    name: 'deliveryDetail',
    hidden: true,
    preViewName: 'arrivalPlan',
    component: () => import('@/views/scc/arrivalPlan/deliveryDetail.vue')
  },
  // 供应商计划协同
  {
    path: 'supplyPlan',
    name: 'supplyPlan',
    component: () => import('@/views/scc/supplyPlan/index.vue')
  },
  {
    path: 'supplyPlanAdd',
    name: 'supplyPlanAdd',
    hidden: true,
    preViewName: 'supplyPlan',
    component: () => import('@/views/scc/supplyPlan/add.vue')
  },
  {
    path: 'supplyPlanDetail',
    name: 'supplyPlanDetail',
    hidden: true,
    preViewName: 'supplyPlan',
    component: () => import('@/views/scc/supplyPlan/detail.vue')
  },
  {
    path: 'supplyDeliveryDetail',
    name: 'supplyDeliveryDetail',
    hidden: true,
    preViewName: 'supplyPlan',
    component: () => import('@/views/scc/supplyPlan/deliveryDetail.vue')
  },

  {
    path: 'drugBatchNo',
    name: 'DrugBatchNo',
    component: () => import('@/views/drugManage/drugBatchNo')
  },
  {
    path: 'drugBatchNoDetail',
    name: 'DrugBatchNoDetail',
    hidden: true,
    preViewName: 'DrugBatchNo',
    component: () => import('@/views/drugManage/drugBatchNo/detail.vue')
  },
  {
    path: 'enterpriseUserView',
    name: 'EnterpriseUserView',
    preViewName: 'EnterpriseUserInfo',
    hidden: true,
    component: () => import('@/views/acc/sysMg/enterpriseUserInfo/UserView')
  },
  // 用户信息配置
  {
    path: 'enterpriseUserInfo',
    name: 'EnterpriseUserInfo',
    component: () => import('@/views/acc/sysMg/enterpriseUserInfo/UserInfo')
  },
  // 审核
  {
    path: 'enterpriseAudit',
    name: 'EnterpriseAudit',
    component: () => import('@/views/companyRegist/enterpriseAudit/index.vue')
  },
  {
    path: 'enterpriseAuditView',
    name: 'EnterpriseAuditView',
    preViewName: 'EnterpriseAudit',
    hidden: true,
    component: () => import('@/views/companyRegist/companyInfo')
  },
  // 角色权限配置
  {
    path: 'rolePermissionConfiguration',
    name: 'RolePermissionConfiguration',
    component: () => import('@/views/companyRegist/userCompanyRole/index.vue')
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/', hidden: true }
]
export default acc
