<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-card class="box-card" style="width: 100%; overflow: auto">
        <div slot="header" class="clearfix">
          <span style="display: inline-block; margin-top: 7px; font-size: 14px"
            >药材赋码详情</span
          >
          <el-button style="float: right" @click="back"
            ><i class="el-icon-back" />返回</el-button
          >
        </div>
        <div
          style="
            display: flex;
            justify-content: space-between;
            padding-right: 55px;
          "
        >
          <el-form
            ref="basic"
            label-position="top"
            :model="basicFormModel"
            :rules="basicFormRules"
            :disabled="type === 2"
            :inline="true"
            style="flex: 1"
          >
            <el-form-item
              prop="idisCode"
              label="工业标识码："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.idisCode"
                readonly
                clearable
                :maxlength="50"
                :placeholder="type === 2 ? '' : ''"
              />
            </el-form-item>
            <el-form-item
              prop="productName"
              label="药材产品名称："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.productName"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>

            <el-form-item
              prop="batchNo"
              label="生产批号："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.batchNo"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="category"
              label="展示模板："
              class="el-form-item-width"
            >
              <!-- <el-select
                v-model="basicFormModel.category"
                filterable
                clearable
                readonly
                style="width: 100%"
                placeholder=""
              >
                <el-option
                  v-for="item in productNameArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->
              <el-input
                v-model="basicFormModel.tenantName"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="tenantName"
              label="对应鲜品："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.product.freshFood"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="药材统计单位："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.product.statisticUnits"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="药材别名："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.product.medicinalAlias"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item
              prop="price"
              label="贮藏条件："
              class="el-form-item-width"
            >
              <el-input
                v-model="basicFormModel.product.storageCondition"
                readonly
                clearable
                placeholder=""
              />
            </el-form-item>

            <el-row>
              <el-col :span="24">
                <el-form-item
                  prop="remark"
                  label="产品简介："
                  style="width: 90%"
                >
                  <el-input
                    v-model="basicFormModel.product.remark"
                    readonly
                    style="width: 100%"
                    clearable
                    type="textarea"
                    :rows="5"
                    placeholder=""
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="right">
            <div class="flex-row">
              <div class="imgBox">
                <img src="@/assets/icon/logo1.png" alt="" class="header-logo" />
              </div>
              <div class="headerText">中国工业互联网标识服务中心</div>
            </div>
            <div class="productName">{{ basicFormModel.productName }}</div>
            <div class="detail_text">
              <div>
                标识信息:<span>{{ basicFormModel.idisCode }}</span>
              </div>
              <div>
                企业信息:<span>{{ basicFormModel.companyName }}</span>
              </div>
              <div>
                批号信息:<span>{{ basicFormModel.batchNo }}</span>
              </div>
            </div>
            <div v-if="basicFormModel.category == 99999" class="watermalon">
              <div class="bmCode">{{ basicFormModel.code }}</div>
              <div class="antiCode">{{ basicFormModel.checkCode }}</div>
              <img
                src="@/assets/img/watermelon.png"
                style="width: 260px; height: 260px"
              />
            </div>
            <canvas
              v-else
              id="canvas"
              class="imgBox2"
              width="260"
              height="260"
              style="width: 260px; height: 260px"
            />
            <div class="btn">
              <el-button icon="el-icon-download" @click="downloadCanvasImage()"
                >下载图片</el-button
              >
            </div>
          </div>
        </div>
      </el-card>
    </el-container>
  </div>
</template>
<script>
import { draw } from "@/utils/canvas";
import { drawxg } from "@/utils/canvasxg";
import accApi from "@/api/acc/acc";

import { mapGetters } from "vuex";
import QrCode from "qrcode";

export default {
  name: "TagOfDetail",

  data() {
    return {
      type: 0,
      id: null,
      code: null,
      collapse: [1, 2, 3],
      roleList: [],
      productNameArr: [],
      fileListOne: [],
      fileListTwo: [],
      fileListThree: [],
      dialogVisible: false,
      dialogType: 0,
      basicFormModel: {},
      detailForm: {},
      basicFormRules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // 产品
    accApi.productCategoryList().then((res) => {
      this.productNameArr = res.data.map((item) => {
        return {
          label: item.categoryName,
          value: item.id,
          ...item,
        };
      });
    });
    this.id = this.$route.query.id;
    if (this.id) {
      accApi.detailTagDetailApi({ id: this.id }).then((res) => {
        this.basicFormModel = res.data;
        this.$nextTick(() => {
          this.getImg("zysbpt" + res.data.idisCode);
          // if (res.data.codeMode === 30) {
          //   this.code = res.data.codeMode;
          //   this.getImg(`https://${window.location.host}/test?id=${this.id}`);
          // } else if (res.data.codeMode === 40) {
          //   this.getImg(`https://${window.location.host}/test1?id=${this.id}`);
          // } else {
          //   console.log(1111);
          //   this.getImg(
          //     `https://${window.location.host}/mobilePhone?id=${this.id}`
          //   );
          //   console.log(
          //     `https://${window.location.host}/mobilePhone?id=${this.id}`
          //   );
          // }
        });
      });
    }
  },
  mounted() {},
  computed: {
    ...mapGetters(["dictionaryList"]),
  },
  methods: {
    getImg(val) {
      if (val) {
        QrCode.toDataURL(val).then((url) => {
          this.src = url;
          console.log(this.code);
          if (this.code === 30) {
            drawxg(url);
          } else {
            console.log(url);

            draw(url);
          }
        });
      }
    },
    // 返回
    back() {
      this.basicFormModel = {};
      this.$router.replace({ path: "tagDetail" });
    },
    downloadCanvasImage() {
      // 获取 canvas 元素
      const canvas = document.getElementById("canvas");

      // 检查 canvas 是否存在
      if (!canvas) {
        alert("未找到 canvas 元素");
        return;
      }

      // 将 canvas 内容转换为图片 URL
      const imageURL = canvas.toDataURL("image/png");

      // 创建一个临时的 <a> 标签
      const a = document.createElement("a");
      a.href = imageURL; // 设置图片 URL
      a.download = "canvas_image.png"; // 设置下载文件名

      // 将 <a> 标签添加到文档中并触发点击事件
      document.body.appendChild(a);
      a.click();

      // 移除临时的 <a> 标签
      document.body.removeChild(a);
    },
  },
};
</script>

<style scoped lang="scss">
.watermalon {
  width: 100%;
  text-align: center;
  margin-top: 40px;
  position: relative;
  font-size: 8px;
  .bmCode {
    position: absolute;
    bottom: 59px;
    width: 100%;
    text-align: center;
    margin-left: 18.1px;
    font-size: 6.8px;
    color: #45935b;
  }
  .antiCode {
    font-size: 10px;
    position: absolute;
    bottom: 23px;
    width: 100%;
    text-align: center;
  }
  img {
  }
}
.right {
  width: 400px;
  text-align: center;
  padding-top: 20px;
  padding-bottom: 20px;
  border: 1px solid #cdcdcd;
  position: relative;
  .btn {
    position: absolute;
    bottom: 0px;
    right: 0px;
  }
  .imgBox {
    height: 25px;
    width: 28px;
    margin-right: 10px;
  }

  .headerText {
    line-height: 30px;
    color: #555555;
    font-weight: bold;
    font-size: 18px;
  }

  .productName {
    margin-top: 16px;
    margin-bottom: 10px;
    color: #000000;
    font-size: 18px;
    font-weight: bold;
  }

  .detail_text {
    //width: 70%;
    margin-left: 10%;
    margin-top: 20px;

    div {
      text-align: left;
      font-weight: bold;
      font-size: 15px;
      word-break: break-all;
      margin-top: 16px;

      span {
        display: inline-block;
        margin-left: 10px;
        font-weight: lighter;
      }
    }
  }

  .imgBox2 {
    margin: 20px auto;
    margin-top: 64px;
    margin-bottom: 10px;
    width: 260px;
    height: 260px;
  }

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
