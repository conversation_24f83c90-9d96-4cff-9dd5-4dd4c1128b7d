import { getMenuList, getUserInfo, loginByUsername, logout } from '@/api/login'
// eslint-disable-next-line no-unused-vars
import {
  getToken,
  getUserId,
  removeToken,
  setToken,
  setUserId,
  setProjectInfo,
  getProjectInfo
} from '@/utils/auth'

const user = {
  state: {
    user: '',
    userCode: '',
    userId: getUserId(),
    token: getToken(),
    name: '',
    avatar: '',
    userType: '', // 对应后端UserInfo中的platformUserType:平台用户类型 1 平台管理员 2 商户管理员 3 普通用户;
    roles: [],
    menus: [], // 菜单权限
    buttons: [], // 按钮权限
    projectInfo: getProjectInfo(),
    merchantCode: '',
    accountType: '',
    status: ''
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_USERCODE: (state, token) => {
      state.userCode = token
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
      setUserId(userId)
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_ACCOUNTTYPE: (state, accountType) => {
      state.accountType = accountType
    },
    SET_STATUS: (state, status) => {
      state.status = status
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_USERTYPE: (state, userType) => {
      state.userType = userType
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_MENUS: (state, menus) => {
      state.menus = menus
    },
    SET_BUTTONS: (state, buttons) => {
      state.buttons = buttons
    },
    SET_PROJECT_INFO: (state, projectInfo) => {
      state.projectInfo = projectInfo
      setProjectInfo(projectInfo)
    },
    SET_MERCHANTCODE: (state, merchantCode) => {
      state.merchantCode = merchantCode
    }
  },

  actions: {
    // 用户名登录
    LoginByUsername({ commit }, userInfo) {
      const username = userInfo.username.trim()
      return new Promise((resolve, reject) => {
        loginByUsername(username, userInfo.password, userInfo.openid)
          .then(response => {
            const data = response.data
            commit('SET_TOKEN', data)
            setToken(data)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取用户信息
    GetUserInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getUserInfo(state.token)
          .then(response => {
            // 由于mockjs 不支持自定义状态码只能这样hack
            if (!response.data) {
              reject('Verification failed, please login again.')
            }
            const data = response.data
            if (data.roles && data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', data.roles)
            }
            // else {
            //   reject('getInfo: roles must be a non-null array!')
            // }

            if (data.menus && data.menus.length > 0) {
              // 验证返回的menus是否是一个非空数组
              commit('SET_MENUS', data.menus)
            }

            if (data.permissionList && data.permissionList.length > 0) {
              // 验证返回的buttons是否是一个非空数组
              commit('SET_BUTTONS', data.permissionList)
            }
            commit('SET_USERCODE', data.usercode)
            commit('SET_MERCHANTCODE', data.merchantCode)
            commit('SET_NAME', data.username)
            commit('SET_AVATAR', data.avatar)
            commit('SET_USERTYPE', data.platformUserType)
            commit('SET_ACCOUNTTYPE', data.accountType)
            commit('SET_STATUS', data.status)
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取menu信息
    GetMenuList({ commit, state }) {
      return new Promise((resolve, reject) => {
        getMenuList()
          .then(response => {
            // 由于mockjs 不支持自定义状态码只能这样hack
            if (!response.data) {
              reject('Verification failed, please login again.')
            }
            const data = response.data.map(item => {
              return {
                ...item,
                children: item.children.filter(child => child.title !== '小程序管理')
              }
            })
            if (state.accountType === '2' && state.status === '0') {
              commit('SET_MENUS', [
                {
                  'url': '',
                  'title': '中药行业工业互联网标识应用平台',
                  'titleEn': null,
                  'menuIcon': 'el-icon-s-order',
                  'menuCode': 'accPortal',
                  'isShow': '1',
                  'level': '0',
                  'children': [
                    {
                      'url': '',
                      'title': '企业注册',
                      'titleEn': null,
                      'menuIcon': 'businessRegistration',
                      'menuCode': 'companyRegist',
                      'isShow': '1',
                      'level': '1',
                      'children': [
                        {
                          'url': 'CompanyInfo',
                          'title': '企业信息录入',
                          'titleEn': null,
                          'menuIcon': null,
                          'menuCode': 'companyInfo',
                          'isShow': '1',
                          'level': '2',
                          'children': []
                        }
                      ]
                    }
                  ]
                }
              ])
            } else {
              if (data && data.length > 0) {
                // if(state.user==='')
                // 验证返回的menus是否是一个非空数组
                commit('SET_MENUS', data)
              }
            }
            // 仅用于permission.js判断是否hasRoles  没有实际作用
            commit('SET_ROLES', ['ADMIN', 'ROLE_ADMIN'])
            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            removeToken()
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },
    setStoreCode({ commit }, storeCode) {
      commit('SET_STORECODE', storeCode)
    },
    setProjectInfo({ commit }, projectInfo) {
      return new Promise(resolve => {
        commit('SET_PROJECT_INFO', projectInfo)
        resolve()
      })
    }
  }
}

export default user

