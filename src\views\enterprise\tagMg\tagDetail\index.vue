<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="search"
      :button-data="buttonData"
      @return-search="
        (data) => {
          searchHelper.search(data);
        }
      "
      @return-reset="searchHelper.reset"
    />
    <data-table
      ref="dataTable"
      :table-data="searchHelper.dataList"
      :column="column"
      :table-option="tableOption"
      :pagination.sync="searchHelper.pagination"
      @search-event="
        () => {
          searchHelper.handleQuery();
        }
      "
    >
      <template v-slot:syncStatus="{ row }">
        <el-tag
          effect="plain"
          :type="typeList[row.syncStatus].type"
          size="medium"
          >{{ typeList[row.syncStatus].text }}</el-tag
        >
      </template>
    </data-table>
    <simple-data-dialog
      v-if="fileDialog"
      :title="['新增', '修改'][dialogFileType]"
      :visible="true"
      size="small"
    >
      <el-form
        ref="baseForm"
        :model="baseFrom"
        size="mini"
        label-width="90px"
        :rules="baseFromRules"
        @submit.native.prevent
      >
        <el-row>
          <el-col>
            <el-form-item prop="productId" label="关联对象：">
              <el-select
                v-model="baseFrom.productId"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择关联对象"
                @change="changeProduct(baseFrom.productId)"
              >
                <el-option
                  v-for="item in productNameArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <div>
            <el-col>
              <el-form-item
                prop="batchId"
                label="关联批次："
                class="el-form-item-width"
              >
                <el-select
                  v-model="baseFrom.batchId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择关联批次"
                >
                  <el-option
                    v-for="item in productBatchArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="展示模板：" class="el-form-item-width">
                <el-select
                  v-model="baseFrom.templateId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择展示模板"
                  disabled
                >
                  <el-option
                    v-for="item in productTemplateArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="dialogFileType === 0">
              <el-form-item
                prop="ruleId"
                label="生码规则："
                class="el-form-item-width"
              >
                <el-select
                  v-model="baseFrom.ruleId"
                  filterable
                  clearable
                  style="width: 100%"
                  placeholder="请选择生码规则"
                  disabled
                  @change="getInfo(baseFrom.ruleId)"
                >
                  <el-option
                    v-for="item in ruleArr"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col v-for="(item, i) in tagRuleTableData" :key="i">
              <el-form-item :label="item.keyCn">
                <el-input
                  :value="
                    item.itemType === 30
                      ? dayjs(new Date()).format(
                          `${item.defaultValue.toUpperCase()}`
                        )
                      : item.defaultValue
                  "
                  readonly
                  clearable
                  style="width: 100%"
                  placeholder=""
                />
              </el-form-item>
            </el-col> -->
          </div>
          <el-col>
            <el-form-item
              prop="codeCount"
              label="生码数量："
              class="el-form-item-width"
            >
              <el-input
                v-model="baseFrom.codeCount"
                clearable
                style="width: 100%"
                placeholder="请输入生码数量"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-footer class="button-container">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="saveClick()">保存</el-button>
      </el-footer>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import acc from "@/api/acc/acc";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import accApi from "@/api/acc/acc";

export default {
  name: "TagDetail",
  components: { SimpleDataDialog, DataSelect, DataTable },
  data() {
    return {
      typeList: {
        10: { type: "warning", text: "未同步" },
        20: { type: "success", text: "已同步" },
        30: { type: "danger", text: "同步异常" },
      },
      buttonData: [
        {
          label: "+ 添加",
          action: this.onAddClick,
          permission: "all",
        },
        {
          label: "导出",
          action: this.exportClick,
          permission: "all",
        },
      ],
      searchHelper: new this.$searchHelper({ api: acc.tagDetailDateListApi }),
      dataList: [],
      tableOption: {
        option: {
          enableSelected: true,
          cellClickSelected: false,
        },
        event: {
          selectionChange: this.onSelectionChange,
        },
      },
      search: {
        searchText: {
          label: "工业标识码/关联对象/关联批号",
          value: null,
          type: "input",
          option: {
            placeholder: "请输入工业标识码/关联对象/关联批号",
          },
        },
        syncStatus: {
          label: "同步状态",
          value: null,
          type: "select",
          option: {
            clearable: false,
            selectOptions: [
              { value: 10, label: "同步中" },
              { value: 20, label: "已同步" },
              { value: 30, label: "同步异常" },
            ],
            placeholder: "请选择同步状态",
          },
        },
      },
      selection: [],
      fileDialog: false,
      dialogFileType: 0,
      id: "",
      baseFrom: {
        templateId: "1899708712015675393",
        ruleId: "1",
      },
      productNameArr: [],
      productTemplateArr: [
        {
          label: "默认模板",
          value: "1899708712015675393",
        },
      ],
      productBatchArr: [],
      ruleArr: [
        {
          label: "默认规则",
          value: "1",
        },
      ],
      ruletagRuleTableDataArr: [],
      tagRuleTableDataCodePreview: undefined,
      baseFromRules: {
        codeCount: [
          {
            required: true,
            message: `请输入生码数量`,
            trigger: `change`,
          },
          { pattern: /^([1-9][0-9]*)$/, message: "只能输入大于0的数字" },
        ],
        productId: [
          {
            required: true,
            message: `请选择绑定产品`,
            trigger: `change`,
          },
        ],
        userCode: [
          // {
          //   required: true,
          //   message: `请选择瓜农`,
          //   trigger: `change`
          // }
        ],
        templateId: [
          {
            required: true,
            message: `请选择绑定模板`,
            trigger: `change`,
          },
        ],
        ruleId: [
          {
            required: true,
            message: `请选择生码规则`,
            trigger: `change`,
          },
        ],
        batchId: [
          {
            required: true,
            message: `请选择绑定批次`,
            trigger: `change`,
          },
        ],
        remark: [
          {
            message: `备注不能超过500`,
            max: 500,
          },
        ],
      },
      column: {
        // 表头
        data: [
          {
            label: "序号",
            prop: "index",
            width: "60px",
            sortable: false,
          },
          {
            label: "工业标识码",
            prop: "idisCode",
          },
          {
            label: "同步状态",
            prop: "syncStatus",
            slotName: "syncStatus",
            width: "100px",
            sortable: false,
          },
          {
            label: "关联对象",
            prop: "productName",
          },
          {
            label: "关联批号",
            prop: "batchNo",
          },
          {
            label: "展示模板",
            prop: "tenantName",
          },
          {
            label: "创建人",
            prop: "userName",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
        ],
        operation: {
          label: "操作",
          width: "80px",
          data: [
            {
              label: "详情",
              action: this.onEditClick,
              permission: "all",
            },
          ],
        },
      },
    };
  },
  watch: {
    // "baseFrom.ruleId": {
    //   handler(val) {
    //     if (!val) {
    //       this.tagRuleTableData = [];
    //       this.tagRuleTableDataCodePreview = undefined;
    //     }
    //   },
    // },
  },
  created() {
    // 产品
    accApi.drugListAll().then((res) => {
      this.productNameArr = res.data.map((item) => {
        return {
          label: item.medicinalProductName,
          value: item.id,
          ...item,
        };
      });
    });
  },
  mounted() {
    this.searchHelper.handleQuery();
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection.map((e) => e.id);
    },
    exportClick() {
      if (this.selection.length > 0) {
        acc.exportTagDetailApi({ ids: this.selection });
      } else {
        acc.exportTagDetailApi(this.searchHelper.getSearchParams());
      }
      this.$refs.dataTable.$refs.multipleTable.clearSelection();
    },
    onEditClick(row) {
      this.$router.replace({ path: "tagOfDetail", query: { id: row.id } });
    },
    changeProduct(val) {
      // this.$set(this.baseFrom, "templateId", undefined);
      // this.$set(this.baseFrom, "ruleId", undefined);
      this.$set(this.baseFrom, "batchId", undefined);
      // this.$set(this.baseFrom, "userCode", undefined);
      this.$refs.baseForm.clearValidate();
      if (val) {
        // 批次
        accApi
          .queryMedicinalBatchList({ medicinalProductId: val })
          .then((res) => {
            this.productBatchArr = res.data.map((item) => {
              return {
                label: item.medicinalBatchCode,
                value: item.id,
                batchNo: item.medicinalBatchCode,
              };
            });
          });
        // 产品模板下拉框
        // accApi.productTemplateSelectListApi({ productId: val }).then((res) => {
        //   this.productTemplateArr = res.data.map((item) => {
        //     return {
        //       label: item.name,
        //       value: item.id,
        //     };
        //   });
        // });
        // 规则下拉框
        // accApi.ruleListApi({ productId: val }).then((res) => {
        //   this.ruleArr = res.data.map((item) => {
        //     return {
        //       label: item.ruleName,
        //       value: item.id,
        //     };
        //   });
        // });
      } else {
        this.productBatchArr = [];
        this.productTemplateArr = [];

        this.ruleArr = [];
      }
    },
    onAddClick() {
      this.dialogFileType = 0;
      this.fileDialog = true;
    },

    cancel() {
      this.tagRuleTableData = [];
      this.baseFrom = {};
      this.fileDialog = false;
    },
    saveClick() {
      this.$refs.baseForm.validate(() => {
        const list = this.productNameArr.filter(
          (e) => e.value === this.baseFrom.productId
        );
        const batchNo = this.productBatchArr.find(
          (e) => e.value === this.baseFrom.batchId
        ).batchNo;
        this.$set(list[0], "updateTime", undefined);
        this.$set(list[0], "createTime", undefined);
        if (this.dialogFileType === 0) {
          accApi
            .addTagGenerateCode({
              ...this.baseFrom,
              productList: list,
              batchNo,
              codePreview: this.tagRuleTableDataCodePreview,
            })
            .then(() => {
              this.cancel();
              this.searchHelper.handleQuery();
            });
        } else {
          accApi
            .editTagGenerate({
              ...this.baseFrom,
              productList: list,
              batchNo,
              codePreview: this.tagRuleTableDataCodePreview,
            })
            .then(() => {
              this.cancel();
              this.searchHelper.handleQuery();
            });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.el-form-item {
  width: 100%;
}

::v-deep .dialog .el-form-item .el-form-item__label {
  text-align: right !important;
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>
