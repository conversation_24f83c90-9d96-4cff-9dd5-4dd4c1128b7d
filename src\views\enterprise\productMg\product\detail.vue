<template>
  <div class="app-container">
    <el-container :style="{ height: '100%'}">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="基本信息" :name="1">
            <el-form
              ref="basic"
              label-position="top"
              :model="basicFormModel"
              :rules="basicFormRules"
              :inline="true"
            >
              <el-form-item
                prop="productName"
                label="产品名称："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.productName" placeholder="请输入产品名称" />
              </el-form-item>
              <el-form-item
                prop="packageName"
                label="显示名称："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.packageName" placeholder="请输入显示名称" />
              </el-form-item>
              <el-form-item
                prop="category"
                label="产品分类："
                class="el-form-item-width"
              >
                <el-select
                  v-model="basicFormModel.category"
                  placeholder="请选择产品分类"
                >
                  <el-option v-for="item in categoryList" :key="item.value" v-bind="item" />
                </el-select>
              </el-form-item>
              <el-form-item
                prop="productNo"
                label="产品编号："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.productNo" placeholder="请输入产品编号" />
              </el-form-item>
              <el-form-item
                prop="barCode"
                label="产品条码："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.barCode" placeholder="请输入产品条码" />
              </el-form-item>
              <el-form-item
                prop="unit"
                label="计量单位："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.unit" placeholder="请输入计量单位" />
              </el-form-item>
              <el-form-item
                prop="price"
                label="单价："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.price" placeholder="请输入单价" />
              </el-form-item>
              <el-form-item
                prop="remark"
                label="产品简介："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.remark" placeholder="请输入产品简介" />
              </el-form-item>
              <el-form-item
                prop="templateversion"
                label="模板编号："
                class="el-form-item-width"
              >
                <el-input v-model="basicFormModel.templateversion" placeholder="请输入模板编号" />
              </el-form-item>
              <el-form-item
                v-if="action === 'edit'"
                prop="beianNo"
                label="添加附加信息："
                class="el-form-item-width"
              >
                <el-button type="primary" @click="dataEditClick">数据编辑</el-button>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="产品规格/属性" :name="2">
            <virtual-table
              ref="attr-table"
              :enable-search="false"
              auto-height
              :columns="specColumns"
              :table-data="specTableData"
              :edit-rules="specRules"
            >
              <template v-slot:attrNameCn="{row, scope}">
                <el-input
                  v-model="row.attrNameCn"
                  placeholder="请输入属性名"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:attrNameEn="{row, scope}">
                <el-input
                  v-model="row.attrNameEn"
                  placeholder="请输入属性英文名"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:attrIndex="{row, scope}">
                <el-input
                  v-model="row.attrIndex"
                  placeholder="请输入属性排序"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:attrValue="{row, scope}">
                <el-input
                  v-model="row.attrValue"
                  placeholder="请输入属性值"
                  @change="$refs.table.updateStatus(scope)"
                />
              </template>
              <template v-slot:attrType="{row, $index, scope}">
                <el-select
                  v-model="row.attrType"
                  placeholder="请选择属性类型"
                  @change="$refs.table.updateStatus(scope)"
                >
                  <el-option :value="10" label="公有" />
                  <el-option :value="20" label="私有" />
                </el-select>
              </template>
              <template v-slot:operate="{$index}">
                <span class="text_button" @click="deleteSpec($index)">删除</span>
              </template>
            </virtual-table>
            <demo-block
              message="添加属性"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addSpec"
            />
          </el-collapse-item>
          <el-collapse-item title="产品图片" :name="3">
            <virtual-table
              ref="table"
              :enable-search="false"
              auto-height
              :columns="imgColumns"
              :table-data="imgTableData"
              :row-height="100"
            >
              <template v-slot:imgPath="{row}">
                <upload-img :img-list="[{ url: row.imgPath}]" readonly />
              </template>
              <template v-slot:operate="{$index}">
                <span class="text_button" @click="deleteImg($index)">删除</span>
              </template>
            </virtual-table>
            <demo-block
              message="添加图片"
              :icon-class="'el-icon-plus icon-class'"
              @click.native="addImg"
            />
          </el-collapse-item>
        </el-collapse>
      </el-main>
      <el-footer class="button-container">
        <el-button @click="back">返回</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </el-footer>
    </el-container>
    <simple-data-dialog
      v-if="imgDialogVisible"
      title="添加产品图片"
      :visible="true"
      size="small"
    >
      <el-form
        ref="imgForm"
        label-position="top"
        :model="imgFormModel"
        :rules="imgFormRules"
        :inline="true"
      >
        <el-form-item
          prop="fileList"
          label="产品图片："
          class="el-form-item-width"
        >
          <upload-img :limit-count="1" :img-list.sync="imgFormModel.fileList" />
        </el-form-item>
        <el-form-item
          prop="remark"
          label="说明："
          style="width: 100%"
        >
          <el-input v-model="imgFormModel.remark" type="textarea" placeholder="请输入图片说明" maxlength="200" />
        </el-form-item>
      </el-form>
      <el-footer class="button-container">
        <el-button @click="imgDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveImage">保存</el-button>
      </el-footer>
    </simple-data-dialog>
    <simple-data-dialog
      v-if="beianDialogVisible"
      title="附加信息"
      :visible="true"
      size="middle"
    >
      <div style="border: 1px solid #dedede;padding-left: 20px">
        <h4 style="margin-left: -10px" />
        <virtual-table auto-height :columns="beianColumns" :table-data="beianTableData">
          <template v-slot:search>
            <el-button type="primary" @click="beianAddClick">添加</el-button>
          </template>
          <template v-slot:operate="{row, $index}">
            <span class="text_button" @click="deleteBeian(row, $index)">删除</span>
          </template>
        </virtual-table>
      </div>

      <el-footer class="button-container">
        <el-button @click="beianDialogVisible = false">关闭</el-button>
      </el-footer>
    </simple-data-dialog>
    <data-dialog
      :dialog-visible.sync="dialogVisible"
      :dialog-data.sync="dialogData"
      dialog-title="添加备案信息"
      :dialog-rule="dialogRule"
      :confirm-func="confirm"
    />
  </div>
</template>
<script>

import DemoBlock from '@/components/DemoBlock'
import VirtualTable from '@/components/VirtualTable/VirtualTable'
import UploadImg from '@/components/DataDialog/uploadImg'
import SimpleDataDialog from '@/components/SimpleDataDialog/index.vue'
import DataDialog from '@/components/DataDialog/index.vue'
import accApi from '@/api/acc/acc'

export default {
  name: 'ProductDetail',
  components: {
    DataDialog,
    SimpleDataDialog,
    UploadImg,
    DemoBlock,
    VirtualTable
  },
  data() {
    return {
      action: null,
      categoryList: [],
      dialogVisible: false,
      dialogData: {
        attrNameCn: {
          label: '备案地区',
          type: 'input',
          value: null
        },
        attrValue: {
          label: '备案号',
          type: 'input',
          value: null
        }, otherAttr1: {
          label: '中药饮片执行标准',
          type: 'input',
          value: null
        }, otherAttr2: {
          label: '中药配方颗粒执行标准',
          type: 'input',
          value: null
        },
        attrIndex: {
          label: '排序',
          type: 'input',
          value: null
        }
      },
      dialogRule: {
        attrNameCn: [
          { required: true, message: '请输入备案地区', trigger: 'blur' }
        ],
        attrValue: [
          { required: true, message: '请输入备案号', trigger: 'blur' }
        ],
        attrIndex: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ],
        otherAttr1: [
          { required: true, message: '请输入中药饮片执行标准', trigger: 'blur' }
        ],
        otherAttr2: [
          { required: true, message: '请输入中药配方颗粒执行标准', trigger: 'blur' }
        ]
      },
      beianColumns: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '备案地区',
          field: 'attrNameCn'
        },
        {
          title: '备案号',
          field: 'attrValue'
        }, {
          title: '中药饮片执行标准',
          field: 'otherAttr1'
        }, {
          title: '中药配方颗粒执行标准',
          field: 'otherAttr2'
        },
        {
          title: '排序',
          field: 'attrIndex'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      beianTableData: [],
      beianDialogVisible: false,
      imgDialogVisible: false,
      collapse: [1, 2, 3],
      formRules: {
        materialName: [
          { required: true, message: '请输入物资名称', trigger: 'blur' }
        ]
      },
      // 表头
      specColumns: [
        {
          type: 'seq',
          title: '序号'
        },
        {
          title: '属性名',
          field: 'attrNameCn',
          slotName: 'attrNameCn'
        },
        {
          title: '属性英文名',
          field: 'attrNameEn',
          slotName: 'attrNameEn'
        },
        {
          title: '属性排序',
          field: 'attrIndex',
          slotName: 'attrIndex'
        },
        {
          title: '属性值',
          field: 'attrValue',
          slotName: 'attrValue'
        },
        {
          title: '属性类型',
          field: 'attrType',
          slotName: 'attrType'
        },
        {
          title: '操作',
          field: 'operate',
          slotName: 'operate',
          width: '50px'
        }
      ],
      specRules: {
        attrNameCn: [
          { required: true, message: '请输入属性名' }
        ],
        attrNameEn: [
          { required: true, message: '请输入属性英文名' }
        ],
        attrIndex: [
          { required: true, message: '请输入属性排序号' }
        ],
        attrValue: [
          { required: true, message: '请输入属性值' }
        ],
        attrType: [
          { required: true, message: '请输入属性类型' }
        ]
      },
      specTableData: [],
      imgColumns: [{
        type: 'seq',
        title: '序号'
      },
      {
        title: '产品图片',
        field: 'imgPath',
        slotName: 'imgPath'
      },
      {
        title: '说明',
        field: 'remark'
      },
      {
        title: '操作',
        field: 'operate',
        slotName: 'operate',
        width: '50px'
      }
      ],
      imgTableData: [],
      imgFormModel: {
        fileList: [],
        remark: ''
      },
      imgFormRules: {
        fileList: { required: true, message: '请上传图片' },
        remark: { required: true, message: '请输入图片说明' }
      },
      basicFormModel: {
        productName: '',
        packageName: '',
        productNo: '',
        barCode: '',
        unit: '',
        category: '',
        price: '',
        remark: '',
        templateversion: '',
        beianNo: ''
      },
      productImgList: [],
      basicFormRules: {
        productName: [
          { required: true, message: '请输入产品名称' }
        ], packageName: [
          { required: true, message: '请输入显示名称' }
        ], category: [
          { required: true, message: '请选择产品分类', trigger: 'submit' }
        ]
      }
    }
  },
  computed: {},
  mounted() {
    this.getCategoryList()
    this.action = this.$route.query.action
    if (this.action === 'edit') {
      this.queryProduct()
    }
  },
  methods: {
    queryProduct() {
      accApi.queryProduct({ id: this.$route.query.id }).then(res => {
        Object.keys(this.basicFormModel).forEach(key => {
          this.basicFormModel[key] = res.data[key]
        })
        this.basicFormModel.category = `${res.data.category}`
        this.specTableData = res.data.productAttrList
        this.imgTableData = res.data.productImgList
      })
    },
    queryBeian() {
      accApi.queryBeiAnAttrList({ productId: this.$route.query.id, attrType: 30 }).then(res => {
        this.beianTableData = res.data
      })
    },
    addSpec() {
      this.specTableData.push({
        attrType: null,
        attrValue: null,
        attrIndex: null,
        attrNameEn: null,
        attrNameCn: null
      })
    },
    addImg() {
      this.imgDialogVisible = true
      this.imgFormModel = {
        fileList: [],
        remark: ''
      }
    },
    deleteSpec(index) {
      this.specTableData.splice(index, 1)
    },
    deleteImg(index) {
      this.imgTableData.splice(index, 1)
    },
    deleteBeian(row, index) {
      accApi.delProductAttr(row).then(res => {
        this.$message.success(res.message)
        this.beianTableData.splice(index, 1)
      })
    },
    dataEditClick() {
      this.queryBeian()
      this.beianDialogVisible = true
    },
    beianAddClick() {
      this.dialogVisible = true
    },
    confirm(data) {
      accApi.addProductAttr({
        ...data,
        productId: this.$route.query.id,
        attrType: 30
      }).then(res => {
        this.$message.success(res.message)
        this.dialogVisible = false
        this.queryBeian()
      })
    },
    getCategoryList() {
      accApi.productCategoryList().then(res => {
        this.categoryList = res.data.map(item => {
          return {
            label: item.categoryName,
            value: item.id
          }
        })
      })
    },
    back() {
      this.$router.replace('product')
    },
    submit() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          this.$refs['attr-table'].validate(attrValid => {
            if (attrValid) {
              const api = this.action === 'edit' ? 'editProduct' : 'addProduct'
              accApi[api]({
                ...this.basicFormModel,
                id: this.$route.query.id,
                productAttrList: this.specTableData,
                imgList: this.imgTableData
              }).then(res => {
                this.$message.success(res.message)
                this.back()
              })
            }
          })
        }
      })
    },
    saveImage() {
      this.$refs['imgForm'].validate(valid => {
        if (valid) {
          this.imgTableData.push({
            imgPath: this.imgFormModel.fileList[0].url,
            remark: this.imgFormModel.remark
          })
          this.imgDialogVisible = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-select--small, .el-cascader--small, .el-input-number--small {
  width: 100%
}

.button-container {
  line-height: 60px;
  text-align: center;
}
</style>
