<template>
  <div class="pop">
    <div
      class="pop-box"
      :style="{
        width: boxStyle.width + 'px',
        height: boxStyle.height + 'px',
        cursor: flag ? 'move' : '',
      }"
    >
      <img :src="src" ref="moveDom" alt />
      <div
        v-if="flag"
        class="border"
        :style="{
          left: styleObj.left + 'px',
          top: styleObj.top + 'px',
          width: watchSize + 'px',
          height: watchSize + 'px',
        }"
      ></div>
    </div>
    <div
      v-if="flag"
      class="pop-image"
      :style="{
        width: popWidth + 'px',
        height: popHeight + 'px',
      }"
    >
      <img :style="popStyle" :src="src" alt />
    </div>
  </div>
</template>

<script>
export default {
  name: "MagnifyImg",
  props: {
    src: {
      type: String,
      required: true,
    },
    width: {
      type: Number,
      default: 300,
    },
    height: {
      type: Number,
      default: 300,
    },
    zoom: {
      type: Number,
      default: 2,
    },
    // 放大展示区域的宽度
    popWidth: {
      type: Number,
      default: 300,
    },
    // 放大展示区域的高度
    popHeight: {
      type: Number,
      default: 300,
    },
    // 示意区域（放大镜框）的大小
    watchSize: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      moveDom: null,
      img: new Image(),
      flag: false,
      popStyle: "",
      boxStyle: {
        width: this.width,
        height: this.height,
      },
      styleObj: {
        left: 0,
        top: 0,
      },
    };
  },
  mounted() {
    this.moveDom = this.$refs.moveDom;
    this.img.src = this.src;
    // 使用节流优化的移动事件处理
    this.throttledMoveEvent = this.rafThrottle(this.moveEvent);
    this.moveDom.addEventListener("mousemove", this.throttledMoveEvent);
    this.moveDom.addEventListener("mouseleave", this.mouseleaveEvent);
  },
  beforeDestroy() {
    this.moveDom.removeEventListener("mouseleave", this.mouseleaveEvent);
    this.moveDom.removeEventListener("mousemove", this.throttledMoveEvent);
  },
  methods: {
    rafThrottle(fn) {
      let locked = false;
      return function (...args) {
        if (locked) return;
        locked = true;
        window.requestAnimationFrame(() => {
          fn.apply(this, args);
          locked = false;
        });
      };
    },
    moveEvent(e) {
      const event = e || window.event;
      if (event.preventDefault) {
        event.preventDefault();
      } else {
        event.returnValue = false;
      }
      this.flag = true;
      const { offsetX, offsetY } = e;
      const { height, width } = this.boxStyle;

      // 计算示意区域（放大镜框）的位置
      this.styleObj = {
        left:
          offsetX - this.watchSize / 2 > 0
            ? offsetX + this.watchSize / 2 >= width
              ? width - this.watchSize
              : offsetX - this.watchSize / 2
            : 0,
        top:
          offsetY - this.watchSize / 2 > 0
            ? offsetY + this.watchSize / 2 >= height
              ? height - this.watchSize
              : offsetY - this.watchSize / 2
            : 0,
      };

      // 修复放大展示区域的图片位置计算
      const { left, top } = this.styleObj;

      // 关键修复：计算放大展示区域应该显示的图片位置
      // 放大展示区域的尺寸比例应该与示意区域的比例一致
      const scaleX = this.popWidth / this.watchSize; // 放大展示区域宽度 / 示意区域宽度
      const scaleY = this.popHeight / this.watchSize; // 放大展示区域高度 / 示意区域高度

      // 计算放大后图片的尺寸，使其能够正确填充放大展示区域
      const zoomedWidth = width * scaleX;
      const zoomedHeight = height * scaleY;

      this.popStyle = `
        left: -${left * scaleX}px;
        top: -${top * scaleY}px;
        width: ${zoomedWidth}px;
        height: ${zoomedHeight}px;
      `;
    },
    mouseleaveEvent() {
      this.flag = false;
    },
  },
};
</script>

<style scoped lang="scss">
.pop {
  position: relative;
  &-box {
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px dashed rgb(253, 253, 253);
    img {
      width: 100%;
      height: 100%;
    }
    .border {
      position: absolute;
      pointer-events: none;
      background-color: rgba(127, 255, 212, 0.315);
      border: 1px solid rgba(127, 255, 212, 0.8);
    }
  }
  &-image {
    position: absolute;
    top: 0;
    overflow: hidden;
    left: 320px; // 稍微增加一点间距
    border-radius: 4px;
    background: #fff;
    img {
      position: absolute;
    }
  }
}
</style>
