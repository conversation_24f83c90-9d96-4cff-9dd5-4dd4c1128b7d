<template>
  <div class="app-container">
    <!-- 搜索框 -->
    <data-select
      :search-data.sync="searchData"
      :button-data="buttonData"
      @return-search="onSearchClick"
      @return-reset="reset"
    />
    <!-- 表格 -->
    <data-table
      ref="dataTable"
      :table-data="tableData"
      :column="column"
      :pagination.sync="pagination"
      :table-option="tableOption"
      @search-event="queryUserList"
    >
      <template v-slot:isDisable="{ row }">
        {{ row.isDisable === "Y" ? "禁用" : "启用" }}
      </template>
      <template v-slot:userName="{ row }">
        <span
          v-if="
            $store.state.user.userCode !== 'admin' && row.emplCode === 'admin'
          "
          >{{ row["userName"] }}</span
        >
        <el-link v-else type="primary" @click="onDetailClick(row)">{{
          row["userName"]
        }}</el-link>
      </template>
    </data-table>
    <simple-data-dialog
      v-if="funDialog.dialogVisible"
      :visible="funDialog.fakeDialogVisible"
      :size="funDialog.size"
    >
      <template slot="title">
        <span class="el-dialog__title">设置菜单</span>
      </template>
      <div class="tree-container">
        <tree-index
          ref="tree"
          :props="funDialog.props"
          :node-key="funDialog.nodeKey"
          :tree-data="funDialog.treeData"
          :single-check="false"
          :check-strictly="true"
        />
      </div>
      <template slot="footer" class="dialog_btn">
        <el-button @click="funDialogClose">{{ $t("common.cancel") }}</el-button>
        <el-button type="primary" @click="saveFunRole">{{
          $t("common.ok")
        }}</el-button>
      </template>
    </simple-data-dialog>
  </div>
</template>

<script>
import DataSelect from "@/components/DataSelect/index.vue";
import SimpleDataDialog from "@/components/SimpleDataDialog/index.vue";
import DataTable from "@/components/DataTable/index.vue";
import accApi from "@/api/acc/acc";
import TreeIndex from "@/views/acc/sysMg/userRole/tree.vue";

export default {
  name: "Index",
  components: {
    TreeIndex,
    DataSelect,
    SimpleDataDialog,
    DataTable,
  },
  data() {
    return {
      // 搜索栏组件
      searchData: {
        emplCode: {
          label: this.$t("acc.userInfo.emplCode"),
          type: "input",
          value: "",
          option: {
            placeholder:
              this.$t("common.enter") + this.$t("acc.userInfo.emplCode"),
          },
        },
        userName: {
          label: this.$t("acc.userInfo.userName"),
          type: "input",
          value: "",
          option: {
            placeholder:
              this.$t("common.enter") + this.$t("acc.userInfo.userName"),
          },
        },
        // roleCodeList: {
        //   label: this.$t('acc.userInfo.role'),
        //   value: null, type: 'select',
        //   option: {
        //     selectOptions: [],
        //     placeholder: this.$t('common.select') + this.$t('acc.userInfo.role'),
        //     multiple: true
        //   }
        // },
        isDisable: {
          label: this.$t("common.status"),
          type: "select",
          value: "N",
          option: {
            selectOptions: [],
            placeholder: this.$t("common.select") + this.$t("common.status"),
          },
        },
      },
      buttonData: [],
      funDialog: {
        dialogVisible: false,
        fakeDialogVisible: true,
        size: "small",
        treeData: [],
        nodeKey: "menuCode",
        props: {
          label: "menuName",
          children: "menuInfoVOList",
        },
      },
      // 数据表格组件
      tableData: [],
      selection: [],
      selectKeys: [],
      column: {
        // 数据列
        data: [
          { label: "guid", prop: "guid", show: false },
          { label: this.$t("acc.userInfo.emplCode"), prop: "emplCode" },
          {
            label: this.$t("acc.userInfo.userName"),
            prop: "userName",
            slotName: "userName",
          },
          // { label: this.$t('acc.userInfo.mail'), prop: 'mail' },
          { label: this.$t("acc.userInfo.phone"), prop: "phone" },
          // { label: '部门名称', prop: 'spaceName' },
          {
            label: this.$t("common.status"),
            prop: "isDisable",
            slotName: "isDisable",
          },
        ],
        // 表格中的操作列
        operation: {
          width: 100,
          label: this.$t("common.operation"),
          data: [
            {
              label: "设置菜单",
              action: this.setFun,
              permission: "all",
            },
          ],
        },
      },
      // 分页参数
      pagination: {
        pageNum: 1,
        pageSize: this.$constant.initPaginationOption.pageSize,
        total: 0,
      },
      tableOption: {
        option: {
          enableSelected: true,
          cellClickSelected: false,
        },
        event: {
          selectionChange: this.onSelectionChange,
          rowClick: this.onRowClick,
        },
      },
      queryParam: {},
      saveParam: {},
      emplStatusMap: {},
    };
  },
  mounted() {
    // debugger
    this.queryParam = { isDisable: "N" };
    this.initView();
    // this.initRoleOption()
  },
  activated() {},
  methods: {
    // initRoleOption() {
    //   accApi.getRoleList().then((res) => {
    //     res.data.list.forEach((item) => {
    //       this.searchData.roleCodeList.option.selectOptions.push({ label: item.roleName, value: item.roleCode })
    //     })
    //   })
    // },
    initView() {
      // accApi.codeQueryList('USER_IS_DISABLE_TYPE').then(result => {
      //   const selectOptions = []
      //   result.data.forEach(code => {
      //     this.emplStatusMap[code.code] = code.codeName
      //     selectOptions.push({ value: code.code, label: code.codeName })
      //   })
      //
      // })
      this.searchData.isDisable.option.selectOptions = [
        { value: "N", label: "启用" },
        { value: "Y", label: "禁用" },
      ];
      this.queryUserList();
    },
    queryUserList() {
      const data = Object.assign(this.queryParam, this.pagination, {
        accountType: "2",
      });
      accApi.userInfo.queryList(data).then((result) => {
        this.tableData = result.data.list;
        this.pagination = result.data.pagination;
      });
    },
    reset() {
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 20;
      this.queryParam = {};
      this.queryUserList();
    },
    // 搜索按钮
    onSearchClick(data) {
      this.pagination.pageNum = 1;
      this.queryParam = data;
      this.queryUserList();
    },
    // 设置功能
    setFun(data) {
      this.currentRoleCode = data.entCode;
      this.getFunTreeData();
    },
    getFunTreeData() {
      accApi
        .queryMenuInfoWithRole({
          roleCode: this.currentRoleCode,
          relationType: 4,
        })
        .then((selectRes) => {
          accApi.queryMenuInfos().then((res) => {
            this.funDialog.treeData = res.data;
            this.funDialog.dialogVisible = true;
            const selection = [];
            selectRes.data.forEach((item) => {
              selection.push(item.menuCode);
            });
            this.$nextTick(() => {
              this.showFunSelected(selection);
            });
          });
        });
    },
    // 回显角色功能复选框
    showFunSelected(selection) {
      this.$nextTick(() => {
        const parentNodes = this.getParentNodes(selection);
        if (parentNodes.length > 0) {
          selection = selection.concat(parentNodes);
        }
        this.$refs.tree.$refs.treeForm.setCheckedKeys(selection);
        this.$refs.tree.initHalfTree(this.funDialog.treeData);
      });
    },
    getParentNodes(selection) {
      const parentNodes = [];
      for (let i = 0; i < selection.length; i++) {
        const parentNode = this.$refs.tree.$refs.treeForm.getNode(
          selection[i]
        )?.parent;
        if (
          parentNode &&
          parentNode.data.menuCode &&
          !selection.includes(parentNode.data.menuCode)
        ) {
          parentNodes.push(parentNode.data.menuCode);
          selection.push(parentNode.data.menuCode);
        }
      }
      return parentNodes;
    },
    funDialogClose() {
      this.funDialog.dialogVisible = false;
    },
    // 保存角色菜单
    saveFunRole() {
      const checked = this.$refs.tree.$refs.treeForm.getCheckedNodes();
      checked.forEach((item) => {
        item.roleCode = this.currentRoleCode;
      });
      const param = {
        roleCode: this.currentRoleCode,
        roleMenuInfoVOList: checked,
        relationType: 4,
      };
      accApi.saveRoleMenu(param).then((res) => {
        // eslint-disable-next-line eqeqeq
        if (res.code == "SUCCESS") {
          this.$message({
            type: "success",
            message: res.msg,
          });
          this.funDialog.dialogVisible = false;
        } else {
          this.$message({
            type: "error",
            message: res.msg,
          });
        }
      });
    },
    onExcelClick() {
      let params = {};
      if (this.$refs.dataTable.$refs.multipleTable.selection.length === 0) {
        params = Object.assign(this.queryParam);
      } else {
        params.emplCodes =
          this.$refs.dataTable.$refs.multipleTable.selection.map((list) => {
            return list.emplCode;
          });
      }
      accApi.userInfo.exportUserExcel(params).then(() => {
        this.$message.success("导出成功！");
      });
    },
    onDetailClick(row) {
      this.$router.replace({
        path: "companyAccountView",
        query: { action: "view", userView: "userView", entCode: row.entCode },
      });
    },

    onSelectionChange(selection) {
      this.selection = selection;
      this.selectKeys = [];
      selection.forEach((row) => this.selectKeys.push(row["emplCode"]));
    },
    onRowClick(row) {
      const isCheck = !this.selectKeys.includes(row["emplCode"]);
      this.$refs.dataTable.toggleRowSelection([row], isCheck);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-scrollbar__wrap {
  overflow-x: hidden;
}

.scrollbar {
  height: calc(60vh);

  ::v-deep .scrollbar__wrap {
    overflow-x: hidden;
  }
}

::v-deep {
  .el-table tr {
    cursor: pointer;
  }
  .el-checkbox__inner {
    padding: 0;
    border-radius: 2px;

    &:after {
      padding: 0;
    }
  }
}
</style>
