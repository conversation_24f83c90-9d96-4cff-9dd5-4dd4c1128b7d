<template>
  <div class="table-container">
    <div v-if="innerTableOption.option.enableSearch" class="table-search">
      <span class="search-input"><el-input ref="tableSearch" v-model="tableSearchValue" :placeholder="$t('common.enter')+$t('common.keyword')" :suffix-icon="'el-icon-search'" clearable /></span>
      <span class="search-slot"><slot name="search" /></span>
    </div>
    <div :style="{width: tableWidth}">
      <el-table
        v-if="showTable"
        ref="multipleTable"
        :highlight-current-row="innerTableOption.option.highlightCurrentRow?innerTableOption.option.highlightCurrentRow:false"
        :data="showTableData"
        :height="innerTableOption.option.height ? innerTableOption.option.height : tableHeight"
        v-bind="innerTableOption.option"
        :span-method="spanMethod"
        :border="innerTableOption.option.border"
        class="table"
        :header-cell-style="innerTableOption.option.headerCellStyle"
        :row-class-name="(row, rowIndex)=>{ return setrowClassName(row,rowIndex,typeof innerTableOption.option.rowClassName === 'function'?'':innerTableOption.option.rowClassName) }"
        @select="(selection, row)=>{if(typeof innerTableOption.event.select == 'function') innerTableOption.event.select(selection, row)}"
        @select-all="(selection)=>{if(typeof innerTableOption.event.selectAll == 'function') innerTableOption.event.selectAll(selection)}"
        @selection-change="(selection)=>{
          selectionList = selection
          if(typeof innerTableOption.event.selectionChange == 'function') innerTableOption.event.selectionChange(selection)
        }"
        @cell-mouse-enter="(row, columnItem, cell, event)=>{if(typeof innerTableOption.event.cellMouseEnter == 'function') innerTableOption.event.cellMouseEnter(row, columnItem, cell, event)}"
        @cell-mouse-leave="(row, columnItem, cell, event)=>{if(typeof innerTableOption.event.cellMouseLeave == 'function') innerTableOption.event.cellMouseLeave(row, columnItem, cell, event)}"
        @cell-click="(row, columnItem, cell, event)=>{innerTableOption.option.enableSelected&&cellClick(row, columnItem, cell, event);if(typeof innerTableOption.event.cellClick == 'function') {innerTableOption.event.cellClick(row, columnItem, cell, event)}}"
        @cell-dblclick="(row, columnItem, cell, event)=>{if(typeof innerTableOption.event.cellDblclick == 'function') innerTableOption.event.cellDblclick(row, columnItem, cell, event)}"
        @row-click="(row, columnItem, event)=>{if(typeof innerTableOption.event.rowClick == 'function') innerTableOption.event.rowClick(row, columnItem, event)}"
        @row-contextmenu="(row, columnItem, event)=>{if(typeof innerTableOption.event.rowContextmenu == 'function') innerTableOption.event.rowContextmenu(row, columnItem, event)}"
        @row-dblclick="(row, columnItem, event)=>{if(typeof innerTableOption.event.rowDblclick == 'function') innerTableOption.event.rowDblclick(row, columnItem, event)}"
        @header-click="(columnItem, event)=>{if(typeof innerTableOption.event.headerClick == 'function') innerTableOption.event.headerClick(columnItem, event)}"
        @header-contextmenu="(columnItem, event)=>{if(typeof innerTableOption.event.headerContextmenu == 'function') innerTableOption.event.headerContextmenu(columnItem, event)}"
        @sort-change="sortChange"
        @filter-change="(filters)=>{if(typeof innerTableOption.event.filterChange == 'function') innerTableOption.event.filterChange(filters)}"
        @current-change="(currentRow, oldCurrentRow)=>{if(typeof innerTableOption.event.currentRowChange == 'function') innerTableOption.event.currentRowChange(currentRow, oldCurrentRow)}"
        @header-dragend="(newWidth, oldWidth, columnItem, event)=>{if(typeof innerTableOption.event.headerDragend == 'function') innerTableOption.event.headerDragend(newWidth, oldWidth, columnItem, event)}"
        @expand-change="(row, expland)=>{if(typeof innerTableOption.event.expandChange == 'function') innerTableOption.event.expandChange(row, expland)}"
      >
        <el-table-column
          v-if="innerTableOption.option.enableRowExpand"
          type="expand"
        >
          <template slot-scope="scope">
            <slot
              name="rowExpand"
              :$index="scope.$index"
              :row="scope.row"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="innerTableOption.option.enableSelected"
          type="selection"
          :width="innerTableOption.option.selectedWidth"
          :selectable="innerTableOption.option.selectable"
          align="center"
          :reserve-selection="innerTableOption.option.reserveSelection"
        />
        <template v-for="(item,index) in innerColumn.data">
          <data-column
            :key="index"
            :item="item"
            :index="index"
          >
            <template slot-scope="scope">
              <span v-if="scope.data.item.prefix" v-html="typeof scope.data.item.prefix === 'function' ? scope.data.item.prefix(scope.data.row,scope.data.$index) : scope.data.item.prefix" />
              <table-rules
                v-if="scope.data.item.slotName && scope.data.item.rules && Object.keys(scope.data.item.rules).length>0"
                :value="scope.data.row"
                :rules="scope.data.item.rules"
                :size="scope.data.item.size"
                :required="scope.data.item.required"
              >
                <slot
                  :name="scope.data.item.slotName"
                  :$index="scope.data.$index"
                  :row="scope.data.row"
                />
              </table-rules>
              <slot
                v-else-if="scope.data.item.slotName"
                :name="scope.data.item.slotName"
                :$index="scope.data.$index"
                :row="scope.data.row"
              />
              <span
                v-else-if="scope.data.item.format"
                v-html="showDate(scope.data.item.format(scope.data.row,scope.data.$index,false))"
              />
              <el-tag
                v-else-if="scope.data.item.type === 'tag' && !$util.isEmpty(scope.data.row[scope.data.item.prop])"
                :type="$util.constantFilter(scope.data.item.statusMapping, 'type', scope.data.row[scope.data.item.prop])"
              >
                {{ $util.constantFilter(scope.data.item.statusMapping, 'label', scope.data.row[scope.data.item.prop]) }}
              </el-tag>
              <span v-else-if="scope.data.item.prop==='index'">{{ scope.data.$index+1 }}</span>
              <template v-else-if="scope.data.item.prop=== imgProp.name">
                <img-link
                  :html-text="showDate(scope.data.row[scope.data.item.prop])"
                  :url-list="scope.data.row[imgProp.urlList]"
                />
              </template>
              <span v-else v-html="showDate(scope.data.row[scope.data.item.prop])" />
              <span v-if="scope.data.item.suffix" v-html="typeof scope.data.item.suffix === 'function' ? scope.data.item.suffix(scope.data.row,scope.data.$index) : scope.data.item.suffix" />
            </template>
          </data-column>
        </template>

        <!-- 操作 -->
        <el-table-column
          v-if="innerColumn.operation.showOperation"
          :label="innerColumn.operation.label"
          :fixed="innerColumn.operation.fixed"
          :prop="'operation'"
          :width="operationBtn(innerColumn.operation.width,btnLength)"
          class="action"
        >
          <template slot-scope="scope">
            <span
              v-for="item in permissionBtn((typeof innerColumn.operation.data =='function'?innerColumn.operation.data(scope.row,scope):innerColumn.operation.data),true)"
              :key="item.id"
              :class="item.class?item.class:'table-action'"
              @click.stop="emitEvent($event, item.action, scope.row,scope.$index)"
            >{{ item.label }}</span>
            <el-popover
              placement="bottom-start"
              trigger="click"
              :transition="'fade-in-linear-table-coloum-operation'"
              :popper-class="'table-action table-action-down'"
              :popper-options="{ removeOnDestroy: true }"
            >
              <div>
                <span v-for="(item,$index) in permissionBtn((typeof innerColumn.operation.data =='function'?innerColumn.operation.data(scope.row):innerColumn.operation.data),false)" :key="item.id">
                  <span class="table-action-down-span" @click.stop="emitEvent($event, item.action, scope.row)">{{ item.label }}</span>
                  <el-divider v-if="$index!=permissionBtn((typeof innerColumn.operation.data =='function'?innerColumn.operation.data(scope.row):innerColumn.operation.data),false).length-1" />
                </span>
              </div>
              <span v-if="permissionBtn((typeof innerColumn.operation.data =='function'?innerColumn.operation.data(scope.row):innerColumn.operation.data),false).length>0" slot="reference" class="table-action">
                •••
              </span>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div
      v-if="innerTableOption.option.showPagination"
      class="flex-between page"
    >
      <el-pagination
        :small="innerPagination.small"
        :background="innerPagination.background"
        :page-size="innerPagination.pageSize"
        :total="innerPagination.total"
        :page-sizes="innerPagination.pageSizes"
        :popper-class="innerPagination.popperClass"
        :disabled="innerPagination.disabled"
        :hide-on-single-page="innerPagination.hideOnSinglePage"
        layout="total, sizes"
        @size-change="sizeChange"
      />
      <el-pagination
        :current-page="innerPagination.pageNum"
        :background="innerPagination.background"
        :pager-count="innerPagination.pagerCount"
        :page-size="innerPagination.pageSize"
        :prev-text="innerPagination.prevText"
        :next-text="innerPagination.nextText"
        :disabled="innerPagination.disabled"
        layout=" prev, pager, next"
        :total="innerPagination.total"
        :hide-on-single-page="innerPagination.hideOnSinglePage"
        @current-change="currentChange"
        @prev-click="prevClick"
        @next-click="nextClick"
      />
    </div>
  </div>
</template>

<script>
import tableRules from './tableRules'
import DataColumn from './column'
import VueEvent from '@/utils/vue-event'
import Sortable from 'sortablejs'
import ImgLink from '@/components/ImgLink'
import { debounce } from 'lodash'
const spanPlaceHolder = []
export default {
  name: 'DataTable',
  componentName: 'DataTable',
  components: {
    ImgLink,
    tableRules,
    DataColumn
  },
  provide() {
    return {
      dataTable: this
    }
  },
  inject: {
    simpleDataDialog: {
      from: 'simpleDataDialog',
      default: () => { return {} }
    }
  },
  props: {
    tableOption: {
      type: Object, // 表格设置参数，option里面为参数，event里面为事件，里面的参数参考element-ui -改为大写,其中option为enableSelected是true的，则设置是能选择的
      default: () => {
        return { option: {}, event: {}}
      }
    },
    column: { // 列参数,data为列的参数，若对象中存在slotName，则启用插槽，自定义列的内容，插槽名为自己定义的slotName，operation为操作列的数据，支持function，function的话会传入当前行数据
      type: Object,
      default: () => {
        return {
          data: [], // 列
          operation: [] // 操作
        }
      }
    },
    // 数据
    tableData: { // 表格数据
      type: Array,
      default: () => {
        return []
      }
    },
    // 分页
    pagination: { // 分页参数，记得在参数后+。sync进行双向绑定
      type: Object,
      default: null
    },
    // 分页
    drag: { // 分页参数，记得在参数后+。sync进行双向绑定
      type: Boolean,
      default: false
    },
    // 高度
    autoHeight: { // 一般不写，为true表格没有滚动条
      type: Boolean,
      default: false
    },
    tableIndex: {
      type: Number,
      default: 3
    },
    autoSpan: {
      type: [Boolean, Array],
      default: false
    },
    autoResetPagNum: {
      type: Boolean,
      default: true
    },
    imgProp: {
      type: Object,
      default() {
        return {
          name: 'materialName',
          urlList: 'previewSrcList'
        }
      }
    }
  },
  data() {
    return {
      windowHeight: null,
      debounceResize: debounce(this.handleResize, 200),
      tableHeight: null,
      selectionList: [],
      innerTableOption: {
        option: {}, event: {}
      },
      innerColumn: {
        type: Object,
        default: () => {
          return {
            data: [], // 列
            operation: [] // 操作
          }
        }
      },
      // 分页
      innerPagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      initTableOption: this.$constant.initTableOption,
      initPaginationOption: this.$constant.initPaginationOption,
      initColumnOption: this.$constant.initColumnOption,
      showTable: true,
      tableWidth: '100%',
      dragstart: null,
      dragend: null,
      fields: [],
      tableSearchValue: null,
      btnLength: 0
    }
  },
  computed: {
    permissionBtn() {
      var that = this
      return (rows, flag) => {
      // console.log(rows, flag);

        const permissionRows = rows.filter((item) => { return that.$checkBtnPermission(item.permission) })
        if (permissionRows.length > this.btnLength) this.btnLength = permissionRows.length
        if (flag) {
          if (permissionRows.length <= this.tableIndex) return permissionRows
          else return permissionRows.slice(0, this.tableIndex - 1)
        }
        if (permissionRows.length <= this.tableIndex) return []
        else return permissionRows.slice(this.tableIndex - 1, permissionRows.length)
      }
    },
    operationBtn() {
      var that = this
      return (width, dataLenth) => {
        if (width) {
          return width
        } else if (dataLenth >= this.tableIndex) {
          return that.$constant.operationBtnMaXWidth
        } else if (dataLenth === this.tableIndex - 1) {
          return that.$constant.operationBtnMiddleWidth
        } else if (!width) {
          return that.$constant.operationBtnMinWidth
        }
        return width
      }
    },
    showTableData() {
      if (!this.tableSearchValue) {
        return this.setRowIndex(this.tableData, true)
      }
      let tableData = []

      tableData = this.filterTableData(this.tableData, tableData)
      tableData = this.setRowIndex(tableData)
      return tableData
    }

  },
  watch: {
    pagination: {
      handler(val) {
        this.innerPagination = Object.assign({}, this.initPaginationOption, this.innerPagination, val)
      },
      deep: true
    },
    tableOption: {
      handler(val) {
        this.innerTableOption.option = Object.assign({}, this.initTableOption.option, val.option)
        this.innerTableOption.event = Object.assign({}, this.initTableOption.event, val.event)
        if (!this.pagination) {
          this.innerTableOption.option.showPagination = false
        }
      },
      deep: true
    },
    column: {
      handler(val) {
        const that = this
        const showOperation = !!val.operation && Object.keys(val.operation).length !== 0
        this.innerColumn = { operation: Object.assign({ showOperation }, val.operation), data: [] }
        val.data.forEach(value => { that.innerColumn.data.push(Object.assign({}, that.initColumnOption, value)) })
      },
      deep: true
    },
    // 如果外层是dialog  dialog关闭时重置pageNum
    'simpleDataDialog.visible': {
      handler(val) {
        if (!val && this.autoResetPagNum) {
          this.innerPagination.pageNum = 1
          this.$emit('update:pagination', this.innerPagination)
        }
      },
      deep: true
    }
  },
  activated() {
    this.$nextTick(() => {
      if (!this.autoHeight && !this.innerTableOption.option.height) this.resizeTableHeight()
    })
  },
  created() {
    const that = this
    this.innerTableOption.option = Object.assign({}, this.initTableOption.option, this.tableOption.option)
    this.innerTableOption.event = Object.assign({}, this.initTableOption.event, this.tableOption.event)
    this.innerPagination = Object.assign({}, this.initPaginationOption, this.pagination)
    if (!this.pagination) {
      this.innerTableOption.option.showPagination = false
    }
    this.innerColumn = { operation: Object.assign({}, { fixed: 'right', showOperation: !!this.column.operation }, this.column.operation), data: [] }
    this.column.data.forEach((value, index, array) => {
      if (value.slotName && !value.slotNameShowTooltip) { value.showOverflowTooltip = false }
      that.innerColumn.data.push(Object.assign({}, that.initColumnOption, value))
      that.getColunm(that.innerColumn.data[index])
    })
    if (!this.autoHeight && !this.innerTableOption.option.height)VueEvent.$on('data.table.resize', this.resizeTableHeight)

    this.$on('el.dataTable.addField', (field) => {
      if (field) {
        this.fields.push(field)
      }
    })
    this.$on('el.dataTable.removeField', (field) => {
      this.fields.splice(this.fields.indexOf(field), 1)
    })
  },
  mounted() {
    this.handleResize()
    window.addEventListener('resize', this.debounceResize)

    this.$nextTick(() => {
      if (this.drag) {
        setTimeout(() => {
          this.setDraggable()
        }, 500)
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debounceResize)
    VueEvent.$off('data.table.resize')
    this.$off('el.dataTable.addField')
    this.$off('el.dataTable.removeField')
    const tbody = this.$refs.multipleTable.$el.querySelector('.el-table__body-wrapper tbody')
    tbody.removeEventListener('dragstart', this.dragstart)
    tbody.removeEventListener('dragend', this.dragend)
    if (this.autoResetPagNum) {
      this.innerPagination.pageNum = 1
      this.$emit('update:pagination', this.innerPagination)
    }
  },
  methods: {
    handleResize() {
      this.$nextTick(() => {
        if (!this.height && !this.innerTableOption.option.height && !this.autoHeight && this.windowHeight !== window.innerHeight) {
          this.resizeTableHeight()
          this.windowHeight = window.innerHeight
        }
      })
    },
    setRowIndex(tableData, flag) {
      tableData.forEach((value, index, array) => {
        flag && array[index].childrenAll && (array[index].children = [...array[index].childrenAll])
        flag && array[index].childrenAll && this.$delete(array[index], 'childrenAll')
        if (array[index].guid) {
          array[index].rowKeyIndex = value.guid
        } else {
          array[index].rowKeyIndex = index
          // + this.getSixNum()
        }
        //       array[index].rowKeyIndex = index
      })
      return tableData
    },
    filterTableData(tableData, resultTableData, pre) {
      resultTableData = tableData.filter((value, index) => {
        return this.innerColumn.data.some(column => {
          if (!column.show) return false
          if (column.slotName && !column.searchable) return false // 自定义列是否参与搜索
          if (column.format && typeof column.format === 'function' && value[column.prop]) {
            return value.children && value.children.length > 0 ? this.filterTableData(value.children, [], value).length > 0 ? true : column.format(value, index, false).toString().includes(this.tableSearchValue.trim()) : column.format(value, index, false).toString().includes(this.tableSearchValue.trim())
          }
          return value.children && value.children.length > 0 ? this.filterTableData(value.children, [], value).length > 0 ? true : value[column.prop] && value[column.prop].toString().includes(this.tableSearchValue.trim()) : value[column.prop] && value[column.prop].toString().includes(this.tableSearchValue.trim())
        })
      })
      // if (pre) { !pre.childrenAll && (pre.childrenAll = [...pre.children]); pre.children = resultTableData }
      return resultTableData
    },
    // 生成六位随机数
    getSixNum() {
      var mm = Math.random()
      var six = ''
      if (mm > 0.1) {
        six = Math.round(mm * 1000000)
      } else {
        mm += 0.1
        six = Math.round(mm * 1000000)
      }
      return six
    },
    getFilteredTableData() {
      return this.showTableData
    },
    showDate(val) {
      // debugger
      if (this.tableSearchValue && val && val.toString().includes(this.tableSearchValue.trim())) {
        return val.toString().replace(this.tableSearchValue.trim(), `<font class="search-color">${this.tableSearchValue.trim()}</font>`)
      } else {
        return val
      }
    },
    getColunm(column) {
      const that = this
      if (column?.children?.length > 0) {
        column.sortable = false
        column.align = 'center'
        column.children.forEach((value, index, array) => {
          array[index] = Object.assign({}, that.initColumnOption, value)
          that.getColunm(array[index])
        })
      }
    },
    resizeTableHeight(searchHeight) {
      this.$nextTick(() => {
        const searchContainer = document.getElementsByClassName('search-container')
        const transferSelect = document.getElementsByClassName('transfer-select')
        let searchContainerHeight = 0
        if (transferSelect?.length > 0) searchContainerHeight = transferSelect[0].offsetHeight
        else if (searchContainer?.length > 0) searchContainerHeight = searchContainer[0].offsetHeight
        const formContainer = document.getElementsByClassName('form-container')
        let formContainerHeight = 0
        if (formContainer?.length > 0) formContainerHeight = formContainer[0].offsetHeight
        const tableSearch = document.getElementsByClassName('table-search')
        let tableSearchHeight = 0
        if (tableSearch?.length > 0) tableSearchHeight = tableSearch[0].offsetHeight
        const elMainTable = document.getElementsByClassName('el-main-table')
        // debugger
        let elMainTableHeight = 0
        if (elMainTable?.length > 0) elMainTableHeight = 40
        const appInnerContainer = document.getElementsByClassName('app-inner-container')
        let appInnerContainerHeight = 0
        if (appInnerContainer?.length > 0) {
          for (const value of appInnerContainer) {
            if (value) {
              appInnerContainerHeight += value.offsetHeight
            }
          }
        }
        const fixHeight = this.innerTableOption.option.showPagination ? 75 : 29

        this.tableHeight = document.getElementsByClassName('app-container')[0].offsetHeight - (searchHeight || searchContainerHeight) - formContainerHeight - tableSearchHeight - appInnerContainerHeight - fixHeight - elMainTableHeight
        this.tableWidth = (this.$refs.multipleTable?.layout?.scrollY ? `calc(100% + ${this.$refs.multipleTable.layout.gutterWidth}px)` : '100%')
      })
    },
    sortChange(params) {
      const currentColumn = this.innerColumn.data.find((item) => item.prop === params.prop)
      if (typeof this.innerTableOption.event.sortChange === 'function') {
        this.innerTableOption.event.sortChange(params)
      }
      if (params.order === 'ascending') { params.order = 'ASC' }
      if (params.order === 'descending') { params.order = 'DESC' }
      this.innerPagination.pageNum = 1
      // 取消排序的时候，清空orderName排序的字段，默认由后端设定的字段进行排序
      this.innerPagination.orderName = params.order ? params.prop : null
      this.innerPagination.orderType = params.order
      this.innerPagination.columnType = currentColumn.columnType
      this.$emit('update:pagination', this.innerPagination)
      this.$emit('search-event')
    },
    sizeChange(data) {
      if (typeof this.innerTableOption.event.sizeChange === 'function') {
        this.innerTableOption.event.sizeChange(data)
      }
      this.innerPagination.pageSize = data
      this.$emit('update:pagination', this.innerPagination)
      this.$emit('search-event')
      if (!this.autoHeight) this.resizeTableHeight()
    },
    currentChange(data) {
      if (typeof this.innerTableOption.event.currentChange === 'function') {
        this.innerTableOption.event.currentChange(data)
      }
      this.innerPagination.pageNum = data
      this.$emit('update:pagination', this.innerPagination)
      this.$emit('search-event')
    },
    prevClick(data) {
      if (typeof this.innerTableOption.event.prevClick === 'function') {
        this.innerTableOption.event.prevClick(data)
      }
      this.innerPagination.pageNum = data
      this.$emit('update:pagination', this.innerPagination)
    },
    nextClick(data) {
      if (typeof this.innerTableOption.event.nextClick === 'function') {
        this.innerTableOption.event.nextClick(data)
      }
      this.innerPagination.pageNum = data
      this.$emit('update:pagination', this.innerPagination)
    },
    emitEvent($event, click, data, $index) {
      if (typeof click === 'function') click(data, $event, $index)
    },
    // 设置选择行
    toggleRowSelection(rows, selected = true) {
      rows.map((row) => {
        this.$refs.multipleTable.toggleRowSelection(row, selected)
      })
    },
    // 获取选择的行
    getSelectionList() {
      return this.selectionList
    },
    // 清除选择行
    clearSelection() {
      this.$refs.multipleTable.clearSelection()
    },
    // 全选
    toggleAllSelection() {
      this.$refs.multipleTable.toggleAllSelection()
    },
    toggleRowExpansion(row, expanded) {
      this.$refs.multipleTable.toggleRowExpansion(row, expanded)
    },
    setCurrentRow(row) {
      this.$refs.multipleTable.setCurrentRow(row)
    },
    clearSort() {
      this.$refs.multipleTable.clearSort()
    },
    clearFilter(columnKey) {
      this.$refs.multipleTable.clearFilter(columnKey)
    },
    doLayout() {
      this.$refs.multipleTable.doLayout()
    },
    sort(prop, order) {
      this.$refs.multipleTable.doLayout(prop, order)
    },
    searchValidate(callback) {
      this.tableSearchValue = null
      this.$nextTick(() => {
        this.validate(callback)
      })
    },
    // 设置可拖拽
    setDraggable() {
      const table = this.$refs.multipleTable
      const tbody = table.$el.querySelector('.el-table__body-wrapper tbody')
      const $this = this
      // const trArr = tbody.querySelectorAll('tr')
      // const dragStartY = 0
      // const _this = this
      Sortable.create(tbody, {
        onEnd({ newIndex, oldIndex }) {
          const currRow = $this.tableData.splice(oldIndex, 1)[0]
          $this.tableData.splice(newIndex, 0, currRow)
        }
      })
      // $this.$emit('dragend-reload', $this.tableData)

      tbody.addEventListener('dragstart', this.dragstart)
      tbody.addEventListener('dragend', this.dragend)
      // const table = this.$refs.multipleTable
      // const tbody = table.$el.querySelector('.el-table__body-wrapper tbody')
      // const $this = this
      // const trArr = tbody.querySelectorAll('tr')
      // let dragStartY = 0
      // if (trArr) {
      //   trArr.forEach((item, index) => {
      //     item.setAttribute('draggable', true)
      //     item.setAttribute('data-index', index)
      //   })
      // }
      // // 开始拖拽
      // this.dragstart = (e) => {
      //   $this.debounce(() => {
      //     dragStartY = e.y
      //   }, 50)
      // }
      // // 结束拖拽
      // this.dragend = (e) => {
      //   if (!e.target) return
      //   const index = Number(e.target.dataset.index, 10)
      //   const trHeight = e.target.offsetHeight || 41
      //   $this.debounce(async() => {
      //     const dVal = e.y - dragStartY
      //     const absVal = Math.abs(dVal)
      //     let step = 0
      //     if (absVal >= trHeight) {
      //       if (dVal > 0) {
      //         step = Math.floor(dVal / trHeight)
      //         if (step >= $this.showTableData.length - index) {
      //           step = $this.showTableData.length - index - 1
      //         }
      //       } else {
      //         step = Math.ceil(dVal / trHeight)
      //         if (Math.abs(step) > index) {
      //           step = index * -1
      //         }
      //       }
      //       if (index === 0 && step <= 0) {
      //         return
      //       }
      //       if (index === $this.showTableData.length - 1 && step >= 0) {
      //         return
      //       }// 重新获取数据
      //       $this.$emit('dragend-reload', {
      //         id: $this.showTableData[index].id,
      //         step: step * -1
      //       })
      //     }
      //   }, 50)
      // }
      // tbody.addEventListener('dragstart', this.dragstart)
      // tbody.addEventListener('dragend', this.dragend)
    }, // 节流函数
    debounce(fn, delay) {
      clearTimeout(this.timer)
      this.timer = setTimeout(function() {
        fn()
      }, delay)
    },
    // 校验tableRule
    validate(callback) {
      let promise
      // if no callback, return promise
      if (typeof callback !== 'function' && window.Promise) {
        promise = new window.Promise((resolve, reject) => {
          callback = function(valid) {
            valid ? resolve(valid) : reject(valid)
          }
        })
      }

      let valid = true
      let count = 0
      // 如果需要验证的fields为空，调用验证时立刻返回callback
      if (this.fields.length === 0 && callback) {
        callback(true)
      }
      this.fields.forEach(field => {
        count++
        field.validate('', (message, field) => {
          if (message) {
            valid = false
          }
        })
        if (typeof callback === 'function' && count === this.fields.length) {
          callback(valid)
        }
      })

      if (promise) {
        return promise
      } else {
        return valid
      }
    },
    getRowValueStr(rowIndex, columnIndex, isSelection) {
      const value = []
      const columns = this.$refs.multipleTable['columns']
      const row = this.$refs.multipleTable['data'][rowIndex]
      const columnProps = []
      columns.forEach((column, index) => {
        if (index <= columnIndex) {
          const prop = isSelection ? columns[index + 1]['property'] : column['property']
          if (Array.isArray(this.autoSpan)) {
            if (isSelection || this.autoSpan.includes(prop)) {
              columnProps.push(prop)
            }
          } else {
            columnProps.push(prop)
          }
        }
      })
      columnProps.forEach(propertyName => { value.push(row[propertyName]) })
      return value.join('-')
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (typeof this.innerTableOption?.option?.spanMethod === 'function') {
        return this.innerTableOption.option.spanMethod({ row, column, rowIndex, columnIndex })
      } else {
        let rowspan = 1
        let colspan = 1
        let needSpan = true
        // 复选框和后面一列的合并保持相同
        let isSelection = false
        if (Array.isArray(this.autoSpan)) {
          needSpan = this.autoSpan.includes(column['property'] || column['type'])
          isSelection = column['type'] === 'selection'
        }
        if (rowIndex === 0 && columnIndex === 0) {
          spanPlaceHolder.length = 0
        }

        if (this.autoSpan && needSpan) {
          const dataKey = `${rowIndex}-${columnIndex}`
          if (!spanPlaceHolder.includes(dataKey)) {
            spanPlaceHolder.push(dataKey)
            const dataLength = this.tableData.length
            const currentCellValue = this.getRowValueStr(rowIndex, columnIndex, isSelection)
            let tmpRowIndex = rowIndex
            while (tmpRowIndex < dataLength - 1) {
              tmpRowIndex++
              const nextDownCellValue = this.getRowValueStr(tmpRowIndex, columnIndex, isSelection)
              if (currentCellValue === nextDownCellValue) {
                spanPlaceHolder.push(`${tmpRowIndex}-${columnIndex}`)
              } else {
                break
              }
              rowspan++
            }
          } else {
            rowspan = 0
            colspan = 0
          }
        }
        return {
          rowspan,
          colspan
        }
      }
    },
    cellClick(row, columnItem, cell, event) {
      // 不支持点击行选中checkbox
      if (!this.innerTableOption.option.cellClickSelected) {
        return false
      }
      // checkbox禁止选择
      if (this.innerTableOption.option && this.innerTableOption.option.selectable && !this.innerTableOption.option.selectable(row)) {
        return false
      }
      // 操作列
      if (columnItem['property'] === 'operation') return false
      // 不支持点击选中的列
      if (this.innerTableOption.option.excludeCellSelectList.includes(columnItem['property'])) return false
      // this.innerTableOption.option.cellSelectedKey用来区分当前行是否被选中，必须是唯一标识
      // 为兼容之前的使用，可以不指定cellSelectedKey
      const i = this.selectionList.findIndex(item => this.innerTableOption.option.cellSelectedKey && item[this.innerTableOption.option.cellSelectedKey] === row[this.innerTableOption.option.cellSelectedKey] || JSON.stringify(item) === JSON.stringify(row))
      this.toggleRowSelection([row], i === -1)

      // 项目中有些地方通过调用toggleRowSelection该方法已经实现了此功能（比如acc/userInfo），需设置cellClickSelected:false
    },

    setrowClassName(row, rowIndex, val) {
      let className = ''
      if (typeof this.innerTableOption.option.rowClassName === 'function') {
        val = val ? val + ' ' + this.innerTableOption.option.rowClassName(row, rowIndex) : this.innerTableOption.option.rowClassName(row, rowIndex)
      }

      className = this.selectionList.findIndex(item => JSON.stringify(item) === JSON.stringify(row.row)) > -1 ? 'tableSelectBg' : ''
      return val ? val + ' ' + className : className
    },
    backTop() {
      this.$refs.multipleTable.bodyWrapper.scrollTop = 0
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~element-ui/packages/theme-chalk/src/mixins/mixins";
@import "../../styles/variables";
.table-action {
  border-left: 1px solid #efedf0;
  padding: 0 10px;
  cursor: pointer;
  color: $primary-color;
}
.cell span:first-of-type.table-action {
  border-left: none;
  padding-left: 0px;
}
.el-table .cell > .drop {
  width: 5px;
  height: 5px;
  background-color: $--color-success-light;
  border-radius: 5px;
  vertical-align: middle;
}

</style>

<style scoped>
.table_main{
  height: 100%;
}
.el-divider--horizontal {
  margin: 5px 0;
}
.table-container >>> .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f5f5;
}
</style>
<style lang="scss">
@import "../../styles/variables";
.table-action {
  border-left: 1px solid #efedf0;
  padding: 10px 0;
  cursor: pointer;
  color: $primary-color;
}
.table-search {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
