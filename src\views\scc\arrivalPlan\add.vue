<template>
  <div class="app-container">
    <el-container :style="{ height: '100%' }">
      <el-main>
        <el-collapse v-model="collapse">
          <el-collapse-item title="添加到货计划" :name="1">
            <el-form
              ref="basic"
              label-position="right"
              :model="basicFormModel"
              :rules="basicFormRules"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="purchaseOrderCode" label="关联采购单号：">
                    <el-input
                      v-model="basicFormModel.purchaseOrderCode"
                      placeholder="请输入物料编码"
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="materialCode" label="物料选择：">
                    <el-select
                      v-model="basicFormModel.materialCode"
                      placeholder="请选择"
                      style="width: 100%"
                      filterable
                      remote
                      reserve-keyword
                      clearable
                      :remote-method="remoteSearchMaterial"
                      :loading="searchLoading"
                      @change="handleMaterialChange($event)"
                      @visible-change="onSelectVisibleChange"
                    >
                      <div
                        v-infinite-scroll="loadMoreMaterial"
                        class="infinite-list"
                        :infinite-scroll-disabled="
                          noMoreCompanies || loadingMore
                        "
                        :infinite-scroll-distance="10"
                      >
                        <el-option
                          v-for="(item, index) in materialOptions"
                          :key="index"
                          :label="item.materialName"
                          :value="item.materialCode"
                        />
                        <div v-if="loadingMore" class="loading-more">
                          <i class="el-icon-loading"></i>
                          <span>加载中...</span>
                        </div>
                      </div>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    prop="quantity"
                    label="计划数量："
                    class="el-form-item-width"
                  >
                    <el-input
                      v-model="basicFormModel.quantity"
                      type="number"
                      placeholder="请输入计划数量"
                    />
                    <span style="position: absolute; margin-left: 10px">{{
                      unitName
                    }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="planDeliveryTime" label="计划交货时间：">
                    <el-date-picker
                      v-model="basicFormModel.planDeliveryTime"
                      type="date"
                      style="width: 100%"
                      placeholder="请选择"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item prop="supplierEntCode" label="供应商选择：">
                    <el-select
                      v-model="basicFormModel.supplierEntCode"
                      placeholder="请选择"
                      style="width: 100%"
                      filterable
                      clearable
                      remote
                      reserve-keyword
                      :remote-method="remoteSearchSupplier"
                      :loading="searchLoading"
                      @change="changeSupplier($event)"
                      @visible-change="onSelectVisibleChange"
                    >
                      <div
                        v-infinite-scroll="loadMoreSupplier"
                        class="infinite-list"
                        :infinite-scroll-disabled="
                          noMoreCompanies || loadingMore
                        "
                        :infinite-scroll-distance="10"
                      >
                        <el-option
                          v-for="(item, index) in supplierOptions"
                          :key="index"
                          :label="item.companyName"
                          :value="item.entCode"
                        />
                        <div v-if="loadingMore" class="loading-more">
                          <i class="el-icon-loading"></i>
                          <span>加载中...</span>
                        </div>
                      </div>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="receiveAddress" label="收货地址：">
                    <el-input
                      v-model="basicFormModel.receiveAddress"
                      placeholder="请输入收货地址"
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item prop="remark" label="备注：">
                    <el-switch
                      v-model="isRemark"
                      active-text="备注给对方"
                      inactive-text=""
                    />
                    <el-input
                      v-model="basicFormModel.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注"
                      maxlength="200"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <el-footer class="button-container">
              <el-button @click="onCancel">取消</el-button>
              <el-button type="primary" @click="onSave">保存</el-button>
            </el-footer>
          </el-collapse-item>
        </el-collapse>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import accApi from "@/api/acc/acc";
export default {
  name: "ArrivalPlanAdd",
  data() {
    return {
      materialOptions: [],
      supplierOptions: [],
      unitName: "",
      collapse: [1],
      isRemark: true,
      basicFormModel: {
        purchaseOrderCode: "",
        quantity: "",
        remark: "",
        materialCode: "",
        supplierEntCode: undefined,
        receiveAddress: "",
        planDeliveryTime: "",
      },
      basicFormRules: {
        // medicinalProductName: [
        //   { required: true, message: "请输入物料名称", trigger: "blur" },
        // ],
        materialCode: [
          { required: true, message: "请选择物料类型", trigger: "change" },
        ],
        quantity: [
          { required: true, message: "请输入计划数量", trigger: "blur" },
        ],
        statisticUnits: [
          { required: true, message: "请选择单位", trigger: "change" },
        ],
        planDeliveryTime: [
          { required: true, message: "请选择计划交货时间", trigger: "change" },
        ],
        supplierEntCode: [
          { required: true, message: "请选择供应商", trigger: "change" },
        ],
      },
      materialPagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      supplierPagination: {
        pageNum: 1,
        pageSize: 20,
        total: 0,
      },
      loadingMore: false,
      noMoreCompanies: false,
      currentSearchQuery: "",
      searchLoading: false,
    };
  },
  created() {
    this.selectMaterial();
    this.selectSupplier();
  },
  methods: {
    async remoteSearchSupplier(query) {
      this.currentSearchQuery = query || "";
      this.resetCompanyPagination();

      this.searchLoading = true;
      try {
        const res = await accApi.selectSupplier({
          keyword: query,
          pageNum: this.supplierPagination.pageNum,
          pageSize: this.supplierPagination.pageSize,
        });
        this.supplierOptions = res.data.list || [];
        this.supplierPagination.total = res.data.total || 0;
        this.noMoreCompanies =
          this.supplierOptions.length < this.supplierPagination.pageSize;
      } catch (error) {
        this.supplierOptions = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },
    async loadMoreSupplier() {
      if (this.noMoreCompanies || this.loadingMore) {
        return;
      }

      this.loadingMore = true;
      this.supplierPagination.pageNum++;

      try {
        const res = await accApi.selectSupplier({
          keyword: this.currentSearchQuery,
          pageNum: this.supplierPagination.pageNum,
          pageSize: this.supplierPagination.pageSize,
        });

        const newOptions = res.data.list || [];
        this.supplierOptions = [...this.supplierOptions, ...newOptions];

        // 判断是否还有更多数据
        if (newOptions.length < this.supplierPagination.pageSize) {
          this.noMoreCompanies = true;
        }
      } catch (error) {
        this.supplierPagination.pageNum--; // 回退页码
      } finally {
        this.loadingMore = false;
      }
    },
    changeSupplier(val) {
      if (!val) {
        this.selectSupplier();
      }
    },
    async loadMoreMaterial() {
      if (this.noMoreCompanies || this.loadingMore) {
        return;
      }

      this.loadingMore = true;
      this.materialPagination.pageNum++;

      try {
        const res = await accApi.selectMaterial({
          keyword: this.currentSearchQuery,
          pageNum: this.materialPagination.pageNum,
          pageSize: this.materialPagination.pageSize,
        });

        const newOptions = res.data.list || [];
        this.materialOptions = [...this.materialOptions, ...newOptions];

        // 判断是否还有更多数据
        if (newOptions.length < this.materialPagination.pageSize) {
          this.noMoreCompanies = true;
        }
      } catch (error) {
        this.materialPagination.pageNum--; // 回退页码
      } finally {
        this.loadingMore = false;
      }
    },
    async remoteSearchMaterial(query) {
      this.currentSearchQuery = query || "";
      this.resetCompanyPagination();

      this.searchLoading = true;
      try {
        const res = await accApi.selectMaterial({
          keyword: query,
          pageNum: this.materialPagination.pageNum,
          pageSize: this.materialPagination.pageSize,
        });
        this.materialOptions = res.data.list || [];
        this.materialPagination.total = res.data.total || 0;
        this.noMoreCompanies =
          this.materialOptions.length < this.materialPagination.pageSize;
      } catch (error) {
        this.materialOptions = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },
    // 下拉框显示/隐藏事件
    onSelectVisibleChange(visible) {
      if (!visible) {
        // 下拉框关闭时重置状态
        this.resetCompanyPagination();
      }
    },
    // 重置分页信息
    resetCompanyPagination() {
      this.materialPagination.pageNum = 1;
      this.materialPagination.total = 0;
      this.supplierPagination.pageNum = 1;
      this.supplierPagination.total = 0;
      this.noMoreCompanies = false;
      this.loadingMore = false;
    },
    handleMaterialChange(materialCode) {
      if (materialCode) {
        this.unitName = this.materialOptions.find(
          (item) => item.materialCode === this.basicFormModel.materialCode
        ).unitName;
      } else {
        this.selectMaterial();
      }
    },
    selectMaterial() {
      this.currentSearchQuery = "";
      this.resetCompanyPagination();
      try {
        this.searchLoading = true;
        accApi
          .selectMaterial({
            keyword: "",
            pageNum: this.materialPagination.pageNum,
            pageSize: this.materialPagination.pageSize,
          })
          .then((res) => {
            this.materialOptions = res.data.list || [];
            this.materialPagination.total = res.data.total || 0;
            this.noMoreCompanies =
              this.materialOptions.length < this.materialPagination.pageSize;
          });
      } catch (error) {
        this.materialOptions = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },
    selectSupplier() {
      this.currentSearchQuery = "";
      this.resetCompanyPagination();
      try {
        this.searchLoading = true;
        accApi
          .selectSupplier({
            keyword: "",
            pageNum: this.supplierPagination.pageNum,
            pageSize: this.supplierPagination.pageSize,
          })
          .then((res) => {
            this.supplierOptions = res.data.list || [];
            this.supplierPagination.total = res.data.total || 0;
            this.noMoreCompanies =
              this.supplierOptions.length < this.supplierPagination.pageSize;
          });
      } catch (error) {
        this.supplierOptions = [];
        this.noMoreCompanies = true;
      } finally {
        this.searchLoading = false;
      }
    },
    onCancel() {
      this.$router.replace({
        path: "arrivalPlan",
        query: { activeName: this.$route.query.activeName },
      });
    },
    onSave() {
      this.$refs.basic.validate((valid) => {
        if (valid) {
          // const api = this.action === "add" ? "addDeliveryPlan" : "";
          const tem = {
            ...this.basicFormModel,
            remarkSwitch: this.isRemark ? 1 : 0,
          };
          accApi.addDeliveryPlan(tem).then((res) => {
            this.$message.success("保存成功");
            this.onCancel();
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.button-container {
  line-height: 60px;
  text-align: center;
}
.infinite-list {
  max-height: 300px;
  overflow-y: auto;
}

.loading-more {
  text-align: center;
  padding: 10px;
  color: #999;
  font-size: 14px;
}

.loading-more i {
  margin-right: 5px;
}
</style>
